package com.frontapi.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 带单订单实时盈利推送DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CopyOrderProfitDTO {
    /** 订单ID */
    private Long orderId;
    
    /** 当前实时盈利 */
    private BigDecimal profit;
    
    /** 当前收益率百分比 */
    private BigDecimal profitRate;
    
    /** 订单状态 1:持仓中 2:已平仓 */
    private Integer status;
    
    /** 推送时间 */
    private Date pushTime;
    
    /** 当前价格 */
    private BigDecimal currentPrice;
}
