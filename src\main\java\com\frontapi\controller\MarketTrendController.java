package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.entity.ExchangePairInfo;
import com.frontapi.service.ExchangePairInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/api/market/trend")
public class MarketTrendController {
    private final List<SseEmitter> emitters = new CopyOnWriteArrayList<>();

    @Autowired
    private ExchangePairInfoService exchangePairInfoService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();

    @GetMapping("/stream")
    public SseEmitter streamMarketTrends(   @RequestParam String token) {
        SseEmitter emitter = new SseEmitter(0L); // 永不超时
        emitters.add(emitter);
        emitter.onCompletion(() -> emitters.remove(emitter));
        emitter.onTimeout(() -> emitters.remove(emitter));
        return emitter;
    }

    // 定时任务，每2秒推送一次
    @Scheduled(fixedRate = 2000)
    public void pushMarketTrends() {
        List<ExchangePairInfo> enabledPairs = exchangePairInfoService.list(
                new QueryWrapper<ExchangePairInfo>()
                        .eq("is_enabled", 1)
                        .orderByAsc("sort")
        );
        // 构建推送数据列表
        List<Map<String, Object>> trendList = new java.util.ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        for (ExchangePairInfo pair : enabledPairs) {
            String symbol = pair.getPairName(); // 数据库字段为pair_name
            String redisKey = "binance:ticker:" + symbol;
            Map<String, Object> binanceData = null;
            try {
                String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
                if (tickerJson != null) {
                    binanceData = objectMapper.readValue(tickerJson, Map.class);
                }
            } catch (Exception e) {
                log.warn("解析Redis行情失败: {}", symbol);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("exchangeName", pair.getExchangeName());
            result.put("tokenName", pair.getTokenName());
            result.put("pairName", symbol);
            result.put("logoUrl", pair.getLogoUrl());
            result.put("price", binanceData != null ? binanceData.get("lastPrice") : null);
            result.put("change", binanceData != null ? binanceData.get("priceChangePercent") : null);
            trendList.add(result);
        }
        for (SseEmitter emitter : emitters) {
            try {
                String sseData = "data: " + objectMapper.writeValueAsString(trendList) + "\n\n";
                emitter.send(sseData);
            } catch (Exception e) {
                emitters.remove(emitter);
            }
        }
    }
} 