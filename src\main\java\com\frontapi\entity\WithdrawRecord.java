package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("withdraw_record")
public class WithdrawRecord {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;
    private String username;
    private String registerEmail; // 注册邮箱
    private BigDecimal amount;
    private BigDecimal fee;
    private BigDecimal realAmount;
    private String address;
    private String chainName;
    private Integer status;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 