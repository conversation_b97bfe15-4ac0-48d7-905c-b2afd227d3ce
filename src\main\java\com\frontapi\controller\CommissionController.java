package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.CommissionRecord;
import com.frontapi.service.CommissionRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.CommissionStatsVO;
 
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/commission")
@RequiredArgsConstructor
public class CommissionController {

    @Autowired
    private CommissionRecordService commissionRecordService;
    
 
    private final UserService userService;
    @GetMapping("/list")
    public ApiResponse<Page<CommissionRecord>> list(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam String types) {
            
        // 使用注入的securityUtil实例获取用户ID
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        
        // 创建分页对象
        Page<CommissionRecord> pageInfo = new Page<>(page, size);
        
        // 构建查询条件
        LambdaQueryWrapper<CommissionRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommissionRecord::getUserId, currentUser.getId())
               .in(CommissionRecord::getCommissionType, types.split(","))
               .orderByDesc(CommissionRecord::getCreateTime);
        
        // 执行页查询
        Page<CommissionRecord> result = commissionRecordService.page(pageInfo, wrapper);
        
        return ApiResponse.success(result);
    }

    @GetMapping("/stats")
    public ApiResponse<CommissionStatsVO> getCommissionStats() {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        
        CommissionStatsVO stats = commissionRecordService.getCommissionStats(currentUser.getId());
        return ApiResponse.success(stats);
    }
} 