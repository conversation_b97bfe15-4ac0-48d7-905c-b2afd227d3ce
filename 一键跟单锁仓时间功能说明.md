# 一键跟单锁仓时间功能说明

## 功能概述

实现了基于锁仓时间的一键跟单管理功能。用户在一键跟单时会记录开始时间，取消跟单时需要验证是否满足带单员配置的锁仓时间要求。

## 核心功能

### 1. 跟单时间记录
- **开始跟单时**：自动记录 `follow_start_time = 当前时间`
- **取消跟单时**：验证通过后设置 `follow_start_time = null`
- **清除关系时**：自动设置 `follow_start_time = null`

### 2. 锁仓时间验证
根据带单员的 `copy_config` 配置进行验证：

#### 无锁仓要求 (lock_time = 0)
- 用户可以随时取消跟单
- 不受时间限制

#### 有锁仓要求 (lock_time > 0)
- 必须满足锁仓天数才能取消跟单
- 计算公式：`已跟单天数 >= 配置的锁仓天数`
- 不满足时显示剩余等待天数

## 技术实现

### 数据库字段

#### front_user 表新增字段
```sql
`follow_start_time` datetime DEFAULT NULL COMMENT '一键跟单开始时间'
```

#### copy_config 表相关字段
```sql
`lock_time` int(11) DEFAULT '0' COMMENT '锁仓时间(0为不锁仓，锁仓30天为30，锁仓90天为90)'
```

### API接口

#### 1. 一键跟单 (POST /api/leader/follow)
```json
{
    "leaderId": 123
}
```
**功能变更**：
- 自动记录 `follow_start_time = 当前时间`

#### 2. 取消跟单 (POST /api/leader/unfollow)
**功能变更**：
- 验证锁仓时间要求
- 满足条件时设置 `follow_start_time = null`

### 核心验证逻辑
```java
// 检查锁仓时间
if (user.getFollowStartTime() != null) {
    long followDays = (System.currentTimeMillis() - user.getFollowStartTime().getTime()) / (1000 * 60 * 60 * 24);
    if (followDays < config.getLockTime()) {
        return ApiResponse.error("锁仓期未满，还需等待 " + (config.getLockTime() - followDays) + " 天才能取消跟单");
    }
}
```

## 业务流程

### 1. 一键跟单流程
```
用户选择带单员 → 验证余额条件 → 设置跟单状态 → 记录开始时间 → 锁定账户
```

### 2. 取消跟单流程
```
用户请求取消 → 检查未平仓订单 → 验证锁仓时间 → 清除跟单状态 → 解锁账户
```

### 3. 锁仓时间计算
```
当前时间 - 跟单开始时间 = 已跟单时长（毫秒）
已跟单时长 ÷ (24 * 60 * 60 * 1000) = 已跟单天数
```

## 配置示例

### 短线配置（无锁仓）
```sql
INSERT INTO copy_config (name, lock_time) VALUES ('短线-随进随出', 0);
```
- 用户可随时取消跟单

### 中线配置（锁仓30天）
```sql
INSERT INTO copy_config (name, lock_time) VALUES ('中线-锁仓30天', 30);
```
- 用户必须跟单满30天才能取消

### 长线配置（锁仓90天）
```sql
INSERT INTO copy_config (name, lock_time) VALUES ('长线-锁仓90天', 90);
```
- 用户必须跟单满90天才能取消

## 使用场景

### 场景1：短线跟单（随时可取消）
- 用户跟单短线带单员
- 配置：`lock_time = 0`
- 结果：随时可以取消跟单

### 场景2：中线跟单（锁仓期内）
- 用户跟单中线带单员
- 配置：`lock_time = 30`
- 已跟单：15天
- 结果：还需等待15天才能取消

### 场景3：长线跟单（锁仓期满）
- 用户跟单长线带单员
- 配置：`lock_time = 90`
- 已跟单：95天
- 结果：可以取消跟单

## 错误处理

### 常见错误信息
```
"锁仓期未满，还需等待 X 天才能取消跟单"
"有未平仓订单，不能取消跟单"
```
