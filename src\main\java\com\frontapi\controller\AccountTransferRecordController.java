package com.frontapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.AccountTransferRecord;
import com.frontapi.service.AccountTransferRecordService;
import com.frontapi.vo.UserVO;
import com.frontapi.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/transfer/records")
@RequiredArgsConstructor
public class AccountTransferRecordController {

    @Autowired
    private AccountTransferRecordService transferRecordService;
    private final UserService userService;
    @GetMapping
    public ApiResponse<Page<AccountTransferRecord>> getTransferRecords(
            Authentication authentication,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
                UserVO currentUser = userService.getCurrentUserInfo();
                if (currentUser == null) {
                    return ApiResponse.error("用户未登录");
                }
                
        Page<AccountTransferRecord> records = transferRecordService.getTransferRecords(currentUser.getId(), page, size);
        return ApiResponse.success(records);
    }

 
} 