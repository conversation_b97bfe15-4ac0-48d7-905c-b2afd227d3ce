package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class FuturesOptionOrder {
    private Long id;
    private Long userId;
    private String symbol;
    private String direction;
    private BigDecimal amount;
    private Integer period;
    private Date orderTime;
    private BigDecimal openPrice;
    private Date settleTime;
    private BigDecimal closePrice;
    private BigDecimal profit;
    private Integer status;
    private String remark;
    private Date createTime;
    private Date updateTime;
    @TableField(exist = false)
    private BigDecimal percent;
   
} 