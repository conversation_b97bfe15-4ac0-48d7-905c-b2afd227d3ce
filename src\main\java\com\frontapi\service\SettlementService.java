package com.frontapi.service;

import com.frontapi.entity.DeliveryOrder;

/**
 * 订单结算服务接口
 */
public interface SettlementService {
    
    /**
     * 处理跟单订单的资金结算
     */
    void processFollowOrderSettlement(DeliveryOrder followOrder);
    
    /**
     * 处理带单员订单的资金结算
     */
    void processLeaderOrderSettlement(DeliveryOrder leaderOrder);

    /**
     * 处理开仓手续费扣除和佣金分配
     * 在订单创建完成后调用，扣除开仓手续费并计算佣金分配
     */
    void processOpenCommissionDistribution(DeliveryOrder order);
}
