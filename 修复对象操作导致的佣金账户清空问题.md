# 修复对象操作导致的佣金账户清空问题

## 🚨 **问题描述**

用户反馈：平仓时佣金明细有记录，但解除跟单关系后，佣金账户余额变成了0。

### 问题现象
从用户截图可以看出：
- **佣金明细有记录** - 说明佣金分配逻辑执行了
- **佣金账户余额为0** - 说明在解除关系时，佣金账户被意外清空

### 问题根因
使用 `updateById(entity)` 对象操作时，可能会意外修改不应该修改的字段，导致佣金账户余额被清空。

## 💡 **解决方案**

按照用户要求：**所有数据库操作都改为使用 Mapper XML SQL语句，只修改需要修改的字段，不修改的字段不操作**。

## 🔧 **具体修复内容**

### 1. 修复跟单关系清除操作

**文件**: `src/main/java/com/frontapi/service/impl/FollowRelationCleanupServiceImpl.java`

#### 问题代码（修复前）:
```java
private void cleanupFollowRelation(FrontUser follower) {
    // 使用对象操作，可能影响其他字段
    follower.setIsFollowing(0);
    follower.setFollowStartTime(null);
    follower.setLeaderId(0L);
    follower.setCopyTradeFrozenStatus(0);
    
    int updateResult = frontUserMapper.updateById(follower); // ❌ 对象操作
}
```

#### 修复后:
```java
private void cleanupFollowRelation(FrontUser follower) {
    // 使用SQL命令直接更新，只修改跟单相关字段
    int updateResult = frontUserMapper.clearFollowRelationStatusOnly(follower.getId()); // ✅ SQL操作
}
```

### 2. 新增专门的SQL方法

**文件**: `src/main/java/com/frontapi/mapper/FrontUserMapper.java`

#### 2.1 跟单关系清除
```java
@Update("UPDATE front_user SET is_following = 0, leader_id = 0, follow_start_time = NULL, copy_trade_frozen_status = 0, update_time = NOW() WHERE id = #{userId}")
int clearFollowRelationStatusOnly(@Param("userId") Long userId);
```

#### 2.2 提现操作
```java
@Update("UPDATE front_user SET available_balance = available_balance - #{amount}, frozen_balance = frozen_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int updateBalanceForWithdraw(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
```

#### 2.3 转账操作
```java
@Update("UPDATE front_user SET available_balance = available_balance - #{amount}, update_time = NOW() WHERE id = #{userId}")
int decreaseAvailableBalanceForTransfer(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

@Update("UPDATE front_user SET available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int increaseAvailableBalanceForTransfer(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
```

#### 2.4 账户划转操作
```java
// 资金账户 → 佣金账户
@Update("UPDATE front_user SET available_balance = available_balance - #{amount}, commission_balance = commission_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromFundToCommission(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

// 佣金账户 → 资金账户
@Update("UPDATE front_user SET commission_balance = commission_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromCommissionToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

// 资金账户 → 跟单账户
@Update("UPDATE front_user SET available_balance = available_balance - #{amount}, copy_trade_balance = copy_trade_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromFundToCopy(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

// 跟单账户 → 资金账户
@Update("UPDATE front_user SET copy_trade_balance = copy_trade_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromCopyToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

// 资金账户 → 收益账户
@Update("UPDATE front_user SET available_balance = available_balance - #{amount}, profit_balance = profit_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromFundToProfit(@Param("userId") Long userId, @Param("amount") BigDecimal amount);

// 收益账户 → 资金账户
@Update("UPDATE front_user SET profit_balance = profit_balance - #{amount}, available_balance = available_balance + #{amount}, update_time = NOW() WHERE id = #{userId}")
int transferFromProfitToFund(@Param("userId") Long userId, @Param("amount") BigDecimal amount);
```

### 3. 修复提现服务

**文件**: `src/main/java/com/frontapi/service/impl/WithdrawServiceImpl.java`

#### 修复前:
```java
// 使用对象操作
user.setAvailableBalance(user.getAvailableBalance().subtract(amount));
user.setFrozenBalance(user.getFrozenBalance().add(amount));
frontUserMapper.updateById(user); // ❌ 可能影响其他字段
```

#### 修复后:
```java
// 使用SQL命令，只修改相关字段
int updateResult = frontUserMapper.updateBalanceForWithdraw(user.getId(), amount);
if (updateResult <= 0) {
    throw new RuntimeException("更新用户余额失败");
}
```

### 4. 修复转账服务

**文件**: `src/main/java/com/frontapi/service/impl/TransferServiceImpl.java`

#### 修复前:
```java
// 使用对象操作
fromUser.setAvailableBalance(fromUser.getAvailableBalance().subtract(amount));
frontUserMapper.updateById(fromUser); // ❌ 可能影响其他字段

toUser.setAvailableBalance(toUser.getAvailableBalance().add(realAmount));
frontUserMapper.updateById(toUser); // ❌ 可能影响其他字段
```

#### 修复后:
```java
// 使用SQL命令，只修改相关字段
int deductResult = frontUserMapper.decreaseAvailableBalanceForTransfer(fromUser.getId(), amount);
if (deductResult <= 0) {
    throw new RuntimeException("扣除转出方余额失败");
}

int addResult = frontUserMapper.increaseAvailableBalanceForTransfer(toUser.getId(), realAmount);
if (addResult <= 0) {
    throw new RuntimeException("增加接收方余额失败");
}
```

### 5. 修复账户划转服务

**文件**: `src/main/java/com/frontapi/service/impl/UserServiceImpl.java`

#### 修复前:
```java
// 使用对象操作
deductAccountBalance(user, fromAccountType, amount);
addAccountBalance(user, toAccountType, amount);
userMapper.updateById(user); // ❌ 可能影响其他字段
```

#### 修复后:
```java
// 使用SQL命令，只修改相关字段
int transferResult = executeAccountTransfer(userId, fromAccountType, toAccountType, amount);
if (transferResult <= 0) {
    throw new RuntimeException("账户划转失败");
}
```

#### 新增划转执行方法:
```java
private int executeAccountTransfer(Long userId, String fromAccountType, String toAccountType, BigDecimal amount) {
    String transferKey = fromAccountType + "_to_" + toAccountType;
    
    switch (transferKey) {
        case "fund_to_commission":
            return frontUserMapper.transferFromFundToCommission(userId, amount);
        case "commission_to_fund":
            return frontUserMapper.transferFromCommissionToFund(userId, amount);
        case "fund_to_copy":
            return frontUserMapper.transferFromFundToCopy(userId, amount);
        case "copy_to_fund":
            return frontUserMapper.transferFromCopyToFund(userId, amount);
        case "fund_to_profit":
            return frontUserMapper.transferFromFundToProfit(userId, amount);
        case "profit_to_fund":
            return frontUserMapper.transferFromProfitToFund(userId, amount);
        default:
            throw new RuntimeException("不支持的账户划转类型: " + fromAccountType + " -> " + toAccountType);
    }
}
```

## 🎯 **修复原理**

### 对象操作的问题
```java
// ❌ 对象操作可能的问题
FrontUser user = frontUserMapper.selectById(userId);
user.setIsFollowing(0);  // 想要修改的字段
// 但是 updateById 会更新所有字段，包括可能被其他线程修改的字段
frontUserMapper.updateById(user);
```

### SQL操作的优势
```java
// ✅ SQL操作只修改指定字段
@Update("UPDATE front_user SET is_following = 0, update_time = NOW() WHERE id = #{userId}")
int clearFollowStatus(@Param("userId") Long userId);
```

## 📊 **修复效果**

### 修复前的问题
- 使用对象操作可能意外修改其他字段
- 佣金账户余额可能被清空
- 数据不一致问题

### 修复后的优势
- ✅ 只修改需要修改的字段
- ✅ 保护佣金账户余额不被意外修改
- ✅ 提高数据操作的精确性
- ✅ 避免并发修改问题
- ✅ 提升系统稳定性

## 🔍 **涉及的字段保护**

### 用户账户相关字段
- `available_balance` - 可用余额
- `commission_balance` - 佣金账户余额 ← **重点保护**
- `copy_trade_balance` - 跟单账户余额
- `profit_balance` - 收益账户余额
- `frozen_balance` - 冻结余额

### 跟单关系相关字段
- `is_following` - 跟单状态
- `leader_id` - 带单员ID
- `follow_start_time` - 跟单开始时间
- `copy_trade_frozen_status` - 跟单账户锁定状态

## 📋 **相关文件清单**

### 修改的文件
1. **FrontUserMapper.java** - 新增专门的SQL方法
2. **FollowRelationCleanupServiceImpl.java** - 修复关系清除操作
3. **WithdrawServiceImpl.java** - 修复提现操作
4. **TransferServiceImpl.java** - 修复转账操作
5. **UserServiceImpl.java** - 修复账户划转操作

### 保护的核心逻辑
- 跟单关系清除时不影响佣金账户
- 提现操作时不影响其他账户
- 转账操作时不影响无关字段
- 账户划转时精确操作指定账户

## 🧪 **测试验证**

### 测试要点
1. **佣金账户保护** - 确认解除关系后佣金余额不变
2. **字段精确性** - 确认只修改指定字段
3. **并发安全性** - 确认多线程操作不冲突
4. **数据一致性** - 确认操作前后数据正确

### 验证SQL
```sql
-- 验证佣金账户余额
SELECT id, commission_balance, is_following, leader_id 
FROM front_user WHERE id = ?;

-- 验证操作前后的变化
-- 应该只有跟单相关字段变化，佣金余额保持不变
```

现在所有的数据库操作都使用精确的SQL语句，只修改需要修改的字段，确保佣金账户等重要字段不会被意外修改！
