package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frontapi.entity.UserBankCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserBankCardMapper extends BaseMapper<UserBankCard> {
    
    @Select("SELECT COUNT(*) > 0 FROM user_bank_card WHERE user_id = #{userId} AND card_number = #{cardNumber}")
    boolean existsByUserIdAndCardNo(Long userId, String cardNumber);
} 