package com.frontapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.frontapi.dto.ApiResponse;
import com.frontapi.dto.OrderRequest;
import com.frontapi.entity.FuturesOptionOrder;
import com.frontapi.service.FuturesOptionOrderService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import com.frontapi.service.SsePushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/futures/option")
@RequiredArgsConstructor
public class FuturesOptionOrderController {
    @Autowired
    private FuturesOptionOrderService futuresOptionOrderService;
    private final UserService userService;
    @Autowired
    private SsePushService ssePushService;
    private static final Logger logger = LoggerFactory.getLogger(FuturesOptionOrderController.class);

    @GetMapping("/hold")
    public ApiResponse<IPage<FuturesOptionOrder>> getHoldOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        IPage<FuturesOptionOrder> result = futuresOptionOrderService.getHoldOrdersPage(page, size, currentUser.getId());
        return ApiResponse.success(result);
    }

    @GetMapping("/deal")
    public ApiResponse<IPage<FuturesOptionOrder>> getDealOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        IPage<FuturesOptionOrder> result = futuresOptionOrderService.getDealOrdersPage(page, size, currentUser.getId());
        return ApiResponse.success(result);
    }

    @GetMapping("/profit")
    public ApiResponse<IPage<FuturesOptionOrder>> getAllProfitOrders(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        IPage<FuturesOptionOrder> result = futuresOptionOrderService.getAllProfitOrdersPage(page, size);
        return ApiResponse.success(result);
    }

    /**
     * SSE接口：推送用户持仓订单实时盈利
     * 前端需带token，后端解析userId
     */
    @GetMapping("/hold/profit/stream")
    public SseEmitter holdProfitStream(HttpServletRequest request) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return null;
        }
        Long userId = currentUser.getId();
        logger.info("SSE连接请求, userId={}", userId);
        SseEmitter emitter = ssePushService.createEmitterForUser(userId);
        logger.info("SseEmitter注册完成, userId={}, emitter={}", userId, emitter);
        // 推送一条假数据测试SSE通路
        try {
            
        } catch (Exception e) {
            logger.error("推送假数据失败: {}", e.getMessage());
        }
        return emitter;
    }

    @PostMapping("/order")
    public ApiResponse<?> placeOrder(@RequestBody OrderRequest orderRequest) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        boolean result = futuresOptionOrderService.placeOrder(
            currentUser.getId(),
            orderRequest.getSymbol(),
            orderRequest.getDirection(),
            orderRequest.getAmount(),
            orderRequest.getPeriod()
        );
        if (result) {
            return ApiResponse.success("下单成功");
        } else {
            return ApiResponse.error("下单失败");
        }
    }
} 