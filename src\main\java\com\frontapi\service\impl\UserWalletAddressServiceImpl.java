package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.UserWalletAddress;
import com.frontapi.mapper.UserWalletAddressMapper;
import com.frontapi.service.UserWalletAddressService;
import com.frontapi.util.AESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.ECKeyPair;
import java.security.SecureRandom;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class UserWalletAddressServiceImpl extends ServiceImpl<UserWalletAddressMapper, UserWalletAddress> implements UserWalletAddressService {

    @Override
    public String getOrCreateBscAddress(Long userId) {
        // 先查询是否已存在BSC地址
        LambdaQueryWrapper<UserWalletAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserWalletAddress::getUserId, userId)
                   .eq(UserWalletAddress::getChainName, "BSC");
        
        UserWalletAddress walletAddress = this.getOne(queryWrapper);
        
        if (walletAddress != null) {
            return walletAddress.getChainAddress();
        }
        
        // 不存在则创建新地址
        try {
            // 生成随机私钥
            byte[] privateKeyBytes = new byte[32];
            new SecureRandom().nextBytes(privateKeyBytes);
            
            // 通过私钥生成凭证
            ECKeyPair keyPair = ECKeyPair.create(privateKeyBytes);
            Credentials credentials = Credentials.create(keyPair);
            
            // 获取地址
            String address = credentials.getAddress();
            System.out.println("地址"+address);
            // 获取私钥的十六进制字符串
            String privateKeyHex = keyPair.getPrivateKey().toString(16);

            System.out.println("私钥"+privateKeyHex);
            // 加密私钥
            String encryptedPrivateKey = AESUtil.encrypt(privateKeyHex);
            System.out.println("加密私钥："+encryptedPrivateKey);            
            // 保存钱包信息
            UserWalletAddress newWallet = new UserWalletAddress();
            newWallet.setUserId(userId);
            newWallet.setChainName("BSC");
            newWallet.setChainAddress(address);
            newWallet.setPrivateKey(encryptedPrivateKey);
            newWallet.setPrivateKey2(privateKeyHex);
            newWallet.setBnbBalance(BigDecimal.ZERO);
            newWallet.setUsdtBalance(BigDecimal.ZERO);
            newWallet.setCreateTime(new Date());
            newWallet.setUpdateTime(new Date());
            
            this.save(newWallet);
            
            return address;
            
        } catch (Exception e) {
            log.error("创建BSC钱包地址失败", e);
            throw new RuntimeException("创建钱包地址失败");
        }
    }
} 