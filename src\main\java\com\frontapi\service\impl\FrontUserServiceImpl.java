package com.frontapi.service.impl;

import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.service.FrontUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class FrontUserServiceImpl implements FrontUserService {
    @Autowired
    private FrontUserMapper frontUserMapper;

    @Override
    public List<Map<String, Object>> getLeadersWithConfigAndOrderStats() {
        return frontUserMapper.selectLeadersWithConfigAndOrderStats();
    }
} 