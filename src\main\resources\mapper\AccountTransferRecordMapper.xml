<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.AccountTransferRecordMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.frontapi.entity.AccountTransferRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="from_account_type" property="fromAccountType"/>
        <result column="to_account_type" property="toAccountType"/>
        <result column="amount" property="amount"/>
        <result column="from_balance_before" property="fromBalanceBefore"/>
        <result column="from_balance_after" property="fromBalanceAfter"/>
        <result column="to_balance_before" property="toBalanceBefore"/>
        <result column="to_balance_after" property="toBalanceAfter"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper> 