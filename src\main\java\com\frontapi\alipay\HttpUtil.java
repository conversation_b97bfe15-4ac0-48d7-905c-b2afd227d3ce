package com.frontapi.alipay;

import okhttp3.*;
import java.io.IOException;

public class HttpUtil {
    private static final OkHttpClient client;
    static {
        client = new OkHttpClient();
    }
    /**
     * 发送 GET 请求
     * @param url 请求 URL
     * @return 响应结果
     */
    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();
        try (Response response = client.newCall(request).execute()) {
            ResponseBody body = response.body();
            if (body != null) {
                return body.string();
            }
            return "";
        }
    }
    /**
     * 发送 POST 请求
     * @return 响应结果
     */
    public static String postJson(String url, String json) throws IOException {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(mediaType, json);
        Request.Builder builder = new Request.Builder()
                .url(url);
        builder.post(requestBody);
        Request request = builder.build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            ResponseBody body = response.body();
            if (body != null) {
                return body.string();
            }
            return "";
        }
    }
}