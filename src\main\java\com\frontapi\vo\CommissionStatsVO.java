package com.frontapi.vo;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CommissionStatsVO {
    private BigDecimal totalCommission = BigDecimal.ZERO; // 总赠送金额
    private BigDecimal purchaseCommission = BigDecimal.ZERO; // 推广赠送(type=1)
    private BigDecimal promotionCommission = BigDecimal.ZERO; // 见点赠送(type=2)
    private BigDecimal cultivateCommission = BigDecimal.ZERO; // 管理赠送(type=3)
    private BigDecimal manageCommission = BigDecimal.ZERO; // 分红赠送(type=4)
    private BigDecimal transferAmount = BigDecimal.ZERO; // 转出总额(withdraw_record表)
} 