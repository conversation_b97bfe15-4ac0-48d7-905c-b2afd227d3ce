package com.frontapi.controller;

//import com.frontapi.common.R;
import com.frontapi.dto.ApiResponse;
import com.frontapi.dto.UpdatePasswordDTO;
import com.frontapi.dto.UpdateSecurityPasswordDTO;
import com.frontapi.dto.UpdatePhoneDTO;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserShareVO;
import com.frontapi.vo.UserVO;
import com.frontapi.vo.TeamStatsVO;
import com.frontapi.vo.TeamRecordVO;
import com.frontapi.vo.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;
        import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import com.frontapi.mapper.CommissionRecordMapper;
import com.frontapi.mapper.FuturesOptionOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;

@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    @Autowired
    private CommissionRecordMapper commissionRecordMapper;
    @Autowired
    private FuturesOptionOrderMapper futuresOptionOrderMapper;
   
    @GetMapping("/info")
    public ApiResponse<UserVO> getUserInfo() {
        return ApiResponse.success(userService.getCurrentUserInfo());
    }

    @PostMapping("/password/sendCode")
    public ApiResponse<Void> sendPasswordVerifyCode() {
        // 获取当前登录用户的手机号并发送验证码
        boolean success = userService.sendPasswordVerifyCode();
        if (!success) {
            return ApiResponse.error("验证码发送失败");
        }
        return ApiResponse.success();
    }

    @PostMapping("/password/update")
    public ApiResponse<Void> updatePassword(@RequestBody UpdatePasswordDTO dto) {
        if (StringUtils.isEmpty(dto.getPassword()) || StringUtils.isEmpty(dto.getVerifyCode())) {
            return ApiResponse.error("参数不能为空");
        }
        
        boolean success = userService.updatePassword(dto);
        if (!success) {
            return ApiResponse.error("密码修改失败");
        }
        
        return ApiResponse.success();
    }

    /**
     * 发送安全密码修改验证码
     */
    @PostMapping("/security/password/sendCode")
    public ApiResponse<Void> sendSecurityPasswordVerifyCode() {
        boolean success = userService.sendSecurityPasswordVerifyCode();
        if (!success) {
            return ApiResponse.error("验证码发送失败");
        }
        return ApiResponse.success();
    }

    /**
     * 修改安全密码
     */
    @PostMapping("/security/password/update")
    public ApiResponse<Void> updateSecurityPassword(@RequestBody UpdateSecurityPasswordDTO dto) {
        if (StringUtils.isEmpty(dto.getSecurityPassword()) || StringUtils.isEmpty(dto.getVerifyCode())) {
            return ApiResponse.error("参数不能为空");
        }
        
        boolean success = userService.updateSecurityPassword(dto);
        if (!success) {
            return ApiResponse.error("安全密码修改失败");
        }
        
        return ApiResponse.success();
    }

    /**
     * 发送手机号修改验证码
     */
    @PostMapping("/phone/sendCode")
    public ApiResponse<Void> sendPhoneVerifyCode(@RequestParam("newPhone") String newPhone) {
        if (StringUtils.isEmpty(newPhone)) {
            return ApiResponse.error("新手机号不能为空");
        }
        
        boolean success = userService.sendPhoneVerifyCode(newPhone);
        if (!success) {
            return ApiResponse.error("验证码发送失败");
        }
        return ApiResponse.success();
    }

    /**
     * 修改手机号
     */
    @PostMapping("/phone/update")
    public ApiResponse<Void> updatePhone(@RequestBody UpdatePhoneDTO dto) {
        if (StringUtils.isEmpty(dto.getNewPhone()) || StringUtils.isEmpty(dto.getVerifyCode())) {
            return ApiResponse.error("参数不能为空");
        }
        
        boolean success = userService.updatePhone(dto);
        if (!success) {
            return ApiResponse.error("手机号修改失败");
        }
        
        return ApiResponse.success();
    }

    /**
     * 验证安全密码
     */
    @PostMapping("/verify-security-password")
    public ApiResponse<Void> verifySecurityPassword(@RequestBody Map<String, String> params) {
        String securityPassword = params.get("securityPassword");
        if (StringUtils.isEmpty(securityPassword)) {
            return ApiResponse.error("安全密码不能为空");
        }
        
        boolean verified = userService.verifySecurityPassword(securityPassword);
        if (!verified) {
            return ApiResponse.error("安全密码错误");
        }
        
        return ApiResponse.success();
    }

    /**
     * 获取推荐用户列表
     */
    @GetMapping("/sharelist")
    public ApiResponse<Page<UserShareVO>> getShareList( @RequestParam(defaultValue = "1") Integer page,
    @RequestParam(defaultValue = "10") Integer size) {
         UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) { 
            return ApiResponse.error("用户未登录");
        }
        Page<UserShareVO> result = userService.getShareList(currentUser.getShareCode(), page, size);
         return ApiResponse.success(result);
    }

    /**
     * 账户划转
     */
    @PostMapping("/transfer")
    public ApiResponse<String> transfer(@RequestBody Map<String, Object> params) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }
            
            // 获取参数
            String fromAccountType = (String) params.get("fromAccountType");
            String toAccountType = (String) params.get("toAccountType");
            Object amountObj = params.get("amount");
            String payPassword = (String) params.get("payPassword");
            
            // 参数验证
            if (StringUtils.isEmpty(fromAccountType) || StringUtils.isEmpty(toAccountType)) {
                return ApiResponse.error("账户类型不能为空");
            }
            
            if (amountObj == null) {
                return ApiResponse.error("划转金额不能为空");
            }
            
            BigDecimal amount;
            try {
                amount = new BigDecimal(amountObj.toString());
            } catch (NumberFormatException e) {
                return ApiResponse.error("划转金额格式错误");
            }
            
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return ApiResponse.error("划转金额必须大于0");
            }
            
            if (StringUtils.isEmpty(payPassword)) {
                return ApiResponse.error("支付密码不能为空");
            }
            
            // 验证划转规则
            if (!validateTransferRule(fromAccountType, toAccountType)) {
                return ApiResponse.error("该账户类型不支持此划转");
            }
            
            // 执行划转
            boolean success = userService.executeTransfer(currentUser.getId(), fromAccountType, toAccountType, amount, payPassword);
            
            if (success) {
                return ApiResponse.success("划转成功");
            } else {
                return ApiResponse.error("划转失败");
            }
            
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    /**
     * 分配佣金比例
     */
    @PostMapping("/assign-commission")
    public ApiResponse<Void> assignCommission(@RequestBody Map<String, Object> params) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        String userNo = (String) params.get("userNo");
        Object assignRateObj = params.get("assignRate");
        if (StringUtils.isEmpty(userNo) || assignRateObj == null) {
            return ApiResponse.error("参数不能为空");
        }
        java.math.BigDecimal assignRate;
        try {
            assignRate = new java.math.BigDecimal(assignRateObj.toString());
        } catch (Exception e) {
            return ApiResponse.error("分配比例格式错误");
        }
        if (assignRate.compareTo(java.math.BigDecimal.ZERO) <= 0) {
            return ApiResponse.error("分配比例必须大于0");
        }
        try {
            userService.assignCommission(currentUser, userNo, assignRate);
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取今日总收益、买涨买跌次数
     */
    @GetMapping("/today-stats")
    public ApiResponse<Map<String, Object>> getTodayStats() {
        UserVO user = userService.getCurrentUserInfo();
        if (user == null) return ApiResponse.error("未登录");
        Long userId = user.getId();
        LocalDateTime start = LocalDate.now().atStartOfDay();
        LocalDateTime end = start.plusDays(1).minusNanos(1);
        // 今日总收益
        BigDecimal todayCommission = commissionRecordMapper.sumTodayCommission(userId, start, end);
        // 买涨/买跌次数
        Date startDate = Date.from(start.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(end.atZone(ZoneId.systemDefault()).toInstant());
        int upCount = futuresOptionOrderMapper.countTodayDirection(userId, 1, startDate, endDate);  // 1为买涨
        int downCount = futuresOptionOrderMapper.countTodayDirection(userId, 2, startDate, endDate); // 2为买跌
        HashMap<String, Object> result = new HashMap<>();
        result.put("todayCommission", todayCommission);
        result.put("upCount", upCount);
        result.put("downCount", downCount);
        return ApiResponse.success(result);
    }

    /**
     * 验证划转规则
     */
    private boolean validateTransferRule(String fromAccountType, String toAccountType) {
        // 资金账户 ↔ 跟单账户（双向）
        if (("fund".equals(fromAccountType) && "copy".equals(toAccountType)) || 
            ("copy".equals(fromAccountType) && "fund".equals(toAccountType))) {
            return true;
        }
        
        // 佣金账户 → 资金账户或跟单账户
        if ("commission".equals(fromAccountType) && 
            ("fund".equals(toAccountType) || "copy".equals(toAccountType))) {
            return true;
        }
        
        // 利润账户 → 资金账户或跟单账户
        if ("profit".equals(fromAccountType) && 
            ("fund".equals(toAccountType) || "copy".equals(toAccountType))) {
            return true;
        }
        
        // 其他情况都不允许
        return false;
    }

    /**
     * 团队业绩统计
     */
    @GetMapping("/team-stats")
    public ApiResponse<TeamStatsVO> getTeamStats() {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        TeamStatsVO stats = userService.getTeamStats(currentUser.getId());
        return ApiResponse.success(stats);
    }

    /**
     * 团队记录分页
     */
    @GetMapping("/team-records")
    public ApiResponse<PageResult<TeamRecordVO>> getTeamRecords(
            @RequestParam String type,
            @RequestParam Integer page,
            @RequestParam Integer size) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        PageResult<TeamRecordVO> result = userService.getTeamRecords(currentUser.getId(), type, page, size);
        return ApiResponse.success(result);
    }
} 