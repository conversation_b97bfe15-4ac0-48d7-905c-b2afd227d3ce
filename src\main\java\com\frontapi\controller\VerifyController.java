package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.dto.VerifyCheckRequest;
import com.frontapi.dto.VerifyImageResponse;
import com.frontapi.dto.VerifyCheckResponse;
import com.frontapi.service.VerifyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth/verify")
@RequiredArgsConstructor
public class VerifyController {

    private final VerifyService verifyService;

    @GetMapping("/get")
    public ApiResponse<VerifyImageResponse> getVerifyImage() {
        return ApiResponse.success(verifyService.generateVerifyImage());
    }

    @PostMapping("/check")
    public ApiResponse<VerifyCheckResponse> checkVerify(@RequestBody VerifyCheckRequest request) {
        return ApiResponse.success(verifyService.checkVerify(request.getMoveX(), request.getKey()));
    }
} 