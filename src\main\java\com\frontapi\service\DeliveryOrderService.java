package com.frontapi.service;

import java.math.BigDecimal;
import java.util.List;
import com.frontapi.vo.CopyOrderVO;

public interface DeliveryOrderService {
    BigDecimal getTodayProfit(Long userId);
    BigDecimal getTotalProfit(Long userId);
    List<CopyOrderVO> getTodayOrders(Long userId);
    List<CopyOrderVO> getHistoryOrders(Long userId);
    // 分页
    List<CopyOrderVO> getTodayOrdersPaged(Long userId, int page, int pageSize);
    int countTodayOrders(Long userId);
    List<CopyOrderVO> getHistoryOrdersPaged(Long userId, int page, int pageSize);
    int countHistoryOrders(Long userId);
    // 查询持仓订单
    List<CopyOrderVO> getOpenOrders(Long userId, int page, int pageSize);
    int countOpenOrders(Long userId);
    // 查询盈利订单
    List<CopyOrderVO> getProfitOrders(Long userId, int page, int pageSize);
    int countProfitOrders(Long userId);
    // 查询所有已平仓订单
    List<CopyOrderVO> getClosedOrders(Long userId, int page, int pageSize);
    int countClosedOrders(Long userId);
    // 创建订单
    void createOrder(com.frontapi.entity.DeliveryOrder order);

    /**
     * 创建订单并扣除余额
     */
    boolean createOrderWithBalance(com.frontapi.entity.DeliveryOrder order, BigDecimal marginAmount);

    /**
     * 为跟单用户批量创建订单
     */
    void createFollowOrders(com.frontapi.entity.DeliveryOrder leaderOrder, BigDecimal currentPrice, BigDecimal leaderOriginalBalance);

    /**
     * 获取用户持仓订单的实时盈利数据
     */
    List<com.frontapi.dto.CopyOrderProfitDTO> getUserHoldOrdersProfit(Long userId);

    /**
     * 一键平仓（带单员平仓）
     */
    boolean closePosition(Long orderId, Long userId);

    /**
     * 平仓处理（通用方法，支持手动平仓和自动平仓）
     */
    void processClosePosition(Long leaderOrderId, String closeReason);
}