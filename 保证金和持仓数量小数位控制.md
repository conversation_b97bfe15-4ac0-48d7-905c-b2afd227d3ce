# 保证金和持仓数量小数位控制

## 🎯 **需求说明**

在结算计算过程中控制小数位数：
- **保证金**：保留2位小数
- **持仓数量**：保留3位小数
- **数据库不变**：只在计算时格式化，不修改数据库存储

## 🔧 **实现方案**

### 1. 新增格式化方法

在 `SettlementServiceImpl` 中添加两个辅助方法：

```java
/**
 * 格式化保证金金额（保留2位小数）
 */
private BigDecimal formatMarginAmount(BigDecimal marginAmount) {
    if (marginAmount == null) {
        return BigDecimal.ZERO;
    }
    return marginAmount.setScale(2, RoundingMode.HALF_UP);
}

/**
 * 格式化持仓数量（保留3位小数）
 */
private BigDecimal formatPositionAmount(BigDecimal positionAmount) {
    if (positionAmount == null) {
        return BigDecimal.ZERO;
    }
    return positionAmount.setScale(3, RoundingMode.HALF_UP);
}
```

### 2. 应用格式化的地方

#### 2.1 跟单订单结算
**方法**: `processFollowOrderSettlement`
```java
BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount());

log.info("跟单订单结算数据格式化 - 订单ID: {}, 原始保证金: {}, 格式化保证金: {}, 原始持仓: {}, 格式化持仓: {}",
        followOrder.getId(), followOrder.getMarginAmount(), marginAmount, 
        followOrder.getPositionAmount(), positionAmount);
```

#### 2.2 带单员订单结算
**方法**: `processLeaderOrderSettlement`
```java
BigDecimal marginAmount = formatMarginAmount(leaderOrder.getMarginAmount());
BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount());

log.info("带单员订单结算数据格式化 - 订单ID: {}, 原始保证金: {}, 格式化保证金: {}, 原始持仓: {}, 格式化持仓: {}",
        leaderOrder.getId(), leaderOrder.getMarginAmount(), marginAmount, 
        leaderOrder.getPositionAmount(), positionAmount);
```

#### 2.3 带单员盈利结算
**方法**: `processLeaderProfitSettlement`
```java
BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount());
```

#### 2.4 带单员亏损结算
**方法**: `processLeaderLossSettlement`
```java
BigDecimal formattedMarginAmount = formatMarginAmount(marginAmount);
BigDecimal positionAmount = formatPositionAmount(leaderOrder.getPositionAmount());
```

#### 2.5 跟单员盈利结算
**方法**: `processProfitSettlement`
```java
BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount());
```

#### 2.6 跟单员亏损结算
**方法**: `processLossSettlement`
```java
BigDecimal marginAmount = formatMarginAmount(followOrder.getMarginAmount());
BigDecimal positionAmount = formatPositionAmount(followOrder.getPositionAmount());
```

#### 2.7 开仓手续费扣除
**方法**: `processOpenCommissionDistribution`
```java
BigDecimal positionAmount = formatPositionAmount(order.getPositionAmount());

log.info("开仓手续费计算 - 订单ID: {}, 原始成交数量: {}, 格式化成交数量: {}, 开仓价格: {}, 手续费率: {}%, 开仓手续费: {}",
        order.getId(), order.getPositionAmount(), positionAmount, openPrice, feeRate, openFee);
```

## 📊 **格式化效果示例**

### 保证金格式化（2位小数）
```
原始值: 2349.8010000000000000
格式化: 2349.80

原始值: 397.6648125534000000
格式化: 397.66

原始值: 198.8324062767000000
格式化: 198.83
```

### 持仓数量格式化（3位小数）
```
原始值: 0.10000000
格式化: 0.100

原始值: 0.01692334
格式化: 0.017

原始值: 0.00846167
格式化: 0.008
```

## 🔄 **计算流程**

### 1. 数据获取
```java
// 从数据库获取原始数据
BigDecimal originalMargin = order.getMarginAmount();     // 例如: 2349.8010000000000000
BigDecimal originalPosition = order.getPositionAmount(); // 例如: 0.10000000
```

### 2. 数据格式化
```java
// 格式化用于计算
BigDecimal formattedMargin = formatMarginAmount(originalMargin);     // 2349.80
BigDecimal formattedPosition = formatPositionAmount(originalPosition); // 0.100
```

### 3. 计算使用
```java
// 使用格式化后的数据进行计算
BigDecimal fee = formattedPosition.multiply(closePrice).multiply(feeRate);
returnMarginToUser(userId, formattedMargin, orderId);
```

## ✅ **优势**

### 1. 计算精度控制
- **保证金**：避免过多小数位导致的显示问题
- **持仓数量**：保持合理的精度，避免计算误差

### 2. 数据一致性
- **数据库不变**：原始数据完整保存
- **计算统一**：所有计算使用相同的精度标准

### 3. 日志清晰
- **对比显示**：同时显示原始值和格式化值
- **便于调试**：清楚看到格式化的效果

### 4. 业务友好
- **用户体验**：显示合理的小数位数
- **财务规范**：符合金融业务的精度要求

## 📋 **修改的方法列表**

1. **新增方法**：
   - `formatMarginAmount(BigDecimal)` - 格式化保证金
   - `formatPositionAmount(BigDecimal)` - 格式化持仓数量

2. **修改的方法**：
   - `processFollowOrderSettlement` - 跟单订单结算
   - `processLeaderOrderSettlement` - 带单员订单结算
   - `processLeaderProfitSettlement` - 带单员盈利结算
   - `processLeaderLossSettlement` - 带单员亏损结算
   - `processProfitSettlement` - 跟单员盈利结算
   - `processLossSettlement` - 跟单员亏损结算
   - `processOpenCommissionDistribution` - 开仓手续费扣除

## 🧪 **测试验证**

### 1. 保证金格式化测试
```java
BigDecimal original = new BigDecimal("2349.8010000000000000");
BigDecimal formatted = formatMarginAmount(original);
// 期望结果: 2349.80
```

### 2. 持仓数量格式化测试
```java
BigDecimal original = new BigDecimal("0.01692334");
BigDecimal formatted = formatPositionAmount(original);
// 期望结果: 0.017
```

### 3. 计算结果验证
- 检查日志中的格式化前后对比
- 验证计算结果的精度
- 确认用户账户变动的准确性

## 📈 **预期效果**

实施后应该能够：
1. ✅ **统一精度标准** - 所有计算使用相同的小数位控制
2. ✅ **提高计算准确性** - 避免过多小数位导致的误差
3. ✅ **改善用户体验** - 显示合理的数值精度
4. ✅ **便于调试监控** - 清晰的格式化日志

这个实现确保了在不改变数据库存储的前提下，对计算过程中的关键数值进行合理的精度控制。
