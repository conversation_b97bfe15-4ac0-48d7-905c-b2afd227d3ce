package com.frontapi.util;

import org.springframework.beans.factory.annotation.Autowired;
import com.frontapi.config.UploadPathConfig;
import org.springframework.stereotype.Component;
import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FileUtil {
    
    @Autowired
    private UploadPathConfig uploadPathConfig;
    
    public void init() {
        try {
            String basePath = uploadPathConfig.getUploadPath();
            String path = basePath.endsWith(File.separator) ? basePath : basePath + File.separator;
            log.info("初始化上传目录，基础路径: {}", path);
            
            // 创建主目录
            File uploadDir = new File(path);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                log.info("创建上传主目录: {} {}", path, created ? "成功" : "失败");
            }
            
            // 创建banner目录
            String bannerPath = path + "banner" + File.separator;
            File bannerDir = new File(bannerPath);
            if (!bannerDir.exists()) {
                boolean created = bannerDir.mkdirs();
                log.info("创建banner目录: {} {}", bannerPath, created ? "成功" : "失败");
            }
            
            // 创建当天日期目录
            String datePath = bannerPath + getCurrentDatePath() + File.separator;
            File dateDir = new File(datePath);
            if (!dateDir.exists()) {
                boolean created = dateDir.mkdirs();
                log.info("创建日期目录: {} {}", datePath, created ? "成功" : "失败");
            }
            
            log.info("文件目录初始化完成");
        } catch (Exception e) {
            log.error("创建上传目录失败", e);
        }
    }
    
    // 获取当前日期路径
    public String getCurrentDatePath() {
        return LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // 格式如: 20241208
    }
    
    // 获取完整的banner存储路径
    public String getBannerStoragePath() {
        return uploadPathConfig.getUploadPath() + "banner/" + getCurrentDatePath() + "/";
    }
    
    // 获取banner访问路径
    public String getBannerAccessPath() {
        return "/upload/banner/" + getCurrentDatePath() + "/";
    }
}