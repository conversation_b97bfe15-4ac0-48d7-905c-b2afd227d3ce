package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("user_bank_card")
public class UserBankCard {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("bank_name")
    private String bankName;
    
    @TableField("card_number")
    private String cardNumber;
    
    @TableField("card_holder")
    private String cardHolder;
    
    @TableField("id_number")
    private String idNumber;
    
    private String phone;
    
    @TableField("bank_branch")
    private String bankBranch;
    
    @TableField("is_default")
    private Boolean isDefault;
    
    private Integer status;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 