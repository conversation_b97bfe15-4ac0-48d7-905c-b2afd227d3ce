package com.frontapi.controller;

import com.frontapi.service.FrontUserService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.frontapi.dto.ApiResponse;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/leader")
public class FrontUserController {
    @Autowired
    private FrontUserService frontUserService;

    @GetMapping("/summary")
    public ApiResponse<List<Map<String, Object>>> getLeaderSummary() {
        List<Map<String, Object>> list = frontUserService.getLeadersWithConfigAndOrderStats();
        return ApiResponse.success(list);
    }
} 