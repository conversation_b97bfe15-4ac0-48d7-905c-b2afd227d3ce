package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.dto.TransferDTO;
import com.frontapi.service.TransferService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/transfer")
@RequiredArgsConstructor
public class TransferController {

    private final TransferService transferService;
    private final UserService userService;

    @PostMapping("/create")
    public ApiResponse<Void> createTransfer(@Valid @RequestBody TransferDTO transferDTO) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
  
        // 传递toUserId、金额、备注给service
        transferService.createTransfer(currentUser.getId(), transferDTO);
        return ApiResponse.success();
    }
} 