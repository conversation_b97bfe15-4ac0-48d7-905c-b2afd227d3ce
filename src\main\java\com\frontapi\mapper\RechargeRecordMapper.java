package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.entity.RechargeRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RechargeRecordMapper extends BaseMapper<RechargeRecord> {
    Page<RechargeRecord> selectRechargeRecordPage(Page<RechargeRecord> page, @Param("userId") Long userId);

    @Insert("<script>" +
            "INSERT INTO recharge_record (user_id, username, phone, amount, recharge_type, " +
            "audit_status, remark, update_time, create_time) VALUES " +
            "<foreach collection='records' item='record' separator=','>" +
            "(#{record.userId}, #{record.username}, #{record.phone}, #{record.amount}, " +
            "#{record.rechargeType}, #{record.auditStatus}, #{record.remark}, " +
            "#{record.updateTime}, #{record.createTime})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("records") List<RechargeRecord> records);
} 