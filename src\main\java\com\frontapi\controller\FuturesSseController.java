package com.frontapi.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.ResponseEntity;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("/api/market/futures")
public class FuturesSseController {
    // 存储所有活跃的SSE连接，key为UUID，value为SseEmitter
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();
    // 存储每个连接对应的symbol
    private final Map<String, String> emitterSymbols = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final RestTemplate restTemplate = new RestTemplate();
    private final Map<String, JsonNode> depthCache = new ConcurrentHashMap<>();
    private final Map<String, JsonNode> tickerCache = new ConcurrentHashMap<>();
    private final Map<String, Long> cacheTime = new ConcurrentHashMap<>();
    private final long CACHE_INTERVAL = 1000; // 1秒

    @Value("${binance.api-prefix}")
    private String binanceApiPrefix;

    /**
     * 统一处理symbol，防止为null或空，且去除非法字符
     */
    private String fixSymbol(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            symbol = "BTCUSDT";
        }
        return symbol.replaceAll("[^A-Z0-9\\-_.]", "");
    }

    private void updateBinanceCache(String symbol) {
        symbol = fixSymbol(symbol);
        if(symbol != null) {
            long now = System.currentTimeMillis();
            if (cacheTime.containsKey(symbol) && now - cacheTime.get(symbol) < CACHE_INTERVAL) {
                return;
            }
            try {
                // 替换原有永续接口为现货接口
                String depthUrl = binanceApiPrefix + "/v1/depth?symbol=" + symbol + "&limit=10";
                ResponseEntity<JsonNode> depthResp = restTemplate.getForEntity(depthUrl, JsonNode.class);
                // System.out.println("[Binance Depth] url=" + depthUrl + ", resp=" + depthResp.getBody());
                if (depthResp.getBody() != null) {
                    depthCache.put(symbol, depthResp.getBody());
                }
                // 替换原有永续接口为现货接口
                String tickerUrl = binanceApiPrefix + "/v3/ticker/24hr?symbol=" + symbol;
                ResponseEntity<JsonNode> tickerResp = restTemplate.getForEntity(tickerUrl, JsonNode.class);
//            System.out.println("[Binance Ticker] url=" + tickerUrl + ", resp=" + tickerResp.getBody());
                if (tickerResp.getBody() != null) {
                    tickerCache.put(symbol, tickerResp.getBody());
                }
                cacheTime.put(symbol, now);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamFutures(@RequestParam String symbol, @RequestParam String token) {
        SseEmitter emitter = new SseEmitter(0L); // 不超时
        String uuid = UUID.randomUUID().toString();
        emitters.put(uuid, emitter);
        emitterSymbols.put(uuid, symbol);

        emitter.onCompletion(() -> {
            emitters.remove(uuid);
            emitterSymbols.remove(uuid);
        });
        emitter.onTimeout(() -> {
            emitters.remove(uuid);
            emitterSymbols.remove(uuid);
        });
        emitter.onError((e) -> {
            emitters.remove(uuid);
            emitterSymbols.remove(uuid);
        });

        return emitter;
    }

    // 定时推送任务（建议在主类加@EnableScheduling）
     @Scheduled(fixedRate = 1000)
    public void pushFuturesData() {
        for (Map.Entry<String, SseEmitter> entry : emitters.entrySet()) {
            String uuid = entry.getKey();
            SseEmitter emitter = entry.getValue();
            String symbol = emitterSymbols.get(uuid);
            // 兜底处理，防止symbol为null或空
            if (symbol == null || symbol.trim().isEmpty()) {
                symbol = "BTCUSDT";
            }

            Map<String, Object> data = new HashMap<>();
            data.put("type", "futures_update");
            data.put("symbol", symbol);
            data.put("buyList", getBuyList(symbol));
            data.put("sellList", getSellList(symbol));
            data.put("price", getLatestPrice(symbol));
            data.put("cnyPrice", getCnyPrice(symbol));
            data.put("changeRate", getChangeRate(symbol));

            try {
                emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(data)));
            } catch (IOException e) {
                emitter.completeWithError(e);
                emitters.remove(uuid);
                emitterSymbols.remove(uuid);
            }
        }
    }

    private List<Map<String, String>> getBuyList(String symbol) {
        symbol = fixSymbol(symbol);
        updateBinanceCache(symbol);
        List<Map<String, String>> buyList = new ArrayList<>();
        JsonNode depth = depthCache.get(symbol);
        if (depth != null && depth.has("bids")) {
            for (JsonNode bid : depth.get("bids")) {
                Map<String, String> item = new HashMap<>();
                item.put("price", bid.get(0).asText());
                item.put("amount", bid.get(1).asText());
                buyList.add(item);
            }
        }
        return buyList;
    }
    private List<Map<String, String>> getSellList(String symbol) {
        symbol = fixSymbol(symbol);
        updateBinanceCache(symbol);
        List<Map<String, String>> sellList = new ArrayList<>();
        JsonNode depth = depthCache.get(symbol);
        if (depth != null && depth.has("asks")) {
            for (JsonNode ask : depth.get("asks")) {
                Map<String, String> item = new HashMap<>();
                item.put("price", ask.get(0).asText());
                item.put("amount", ask.get(1).asText());
                sellList.add(item);
            }
        }
        return sellList;
    }
    private String getLatestPrice(String symbol) {
        symbol = fixSymbol(symbol);
        updateBinanceCache(symbol);
        JsonNode ticker = tickerCache.get(symbol);
        if (ticker != null && ticker.has("lastPrice")) {
            return ticker.get("lastPrice").asText();
        }
        return "0";
    }
    private String getChangeRate(String symbol) {
        symbol = fixSymbol(symbol);
        updateBinanceCache(symbol);
        JsonNode ticker = tickerCache.get(symbol);
        if (ticker != null && ticker.has("priceChangePercent")) {
            return ticker.get("priceChangePercent").asText();
        }
        return "0";
    }
    private String getCnyPrice(String symbol) {
        symbol = fixSymbol(symbol);
        String lastPrice = getLatestPrice(symbol);
        try {
            double price = Double.parseDouble(lastPrice);
            return String.format("%.2f", price * 7.2);
        } catch (Exception e) {
            return "0";
        }
    }
} 