package com.frontapi.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import com.frontapi.dto.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/auth")
public class CaptchaController {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @GetMapping("/captcha")
    public ApiResponse<Map<String, String>> getCaptcha() {
        // 生成4位字母数字混合验证码
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(120, 40, 5, 20);
        String code = captcha.getCode();
        String key = UUID.randomUUID().toString();

        // 存到Redis，5分钟有效
        redisTemplate.opsForValue().set("captcha:" + key, code, 5, TimeUnit.MINUTES);

        // 图片转base64
        String base64Img = Base64.getEncoder().encodeToString(captcha.getImageBytes());

        Map<String, String> result = new HashMap<>();
        result.put("key", key);
        result.put("image", "data:image/png;base64," + base64Img);
        
        // 将结果用ApiResponse包装
        return ApiResponse.success(result);
    }
} 