package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("user_wallet_address")
public class UserWalletAddress {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 链名称
     */
    private String chainName;
    
    /**
     * 链地址
     */
    private String chainAddress;
    
    /**
     * 私钥（加密）
     */
    private String privateKey;
     /**
     * 私钥（加密）
     */
    private String privateKey2;
    
    /**
     * BNB余额
     */
    private BigDecimal bnbBalance;
    
    /**
     * USDT余额
     */
    private BigDecimal usdtBalance;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 