# 返佣状态同步更新功能

## 🎯 **功能目标**

在订单结算完成的最后一步，确保返佣状态也被正确设置，避免出现结算状态已更新但返佣状态未同步的问题。

## 🔧 **实现方案**

### 1. **新增Mapper方法**

在 `DeliveryOrderMapper.java` 中添加了同时更新结算状态和返佣状态的方法：

```java
/**
 * 同时更新订单结算状态和返佣状态（确保最终状态一致）
 */
@Update("UPDATE delivery_order SET is_settlement = #{settlementStatus}, rebate_status = #{rebateStatus}, update_time = NOW() WHERE id = #{orderId}")
int updateSettlementAndRebateStatus(@Param("orderId") Long orderId, @Param("settlementStatus") Integer settlementStatus, @Param("rebateStatus") Integer rebateStatus);
```

### 2. **增强结算状态更新方法**

在 `SettlementServiceImpl.java` 中修改了 `updateOrderSettlementStatus` 方法：

#### 方法重载
```java
// 原有方法（向后兼容）
private void updateOrderSettlementStatus(Long orderId, int settlementStatus)

// 新增方法（同时更新返佣状态）
private void updateOrderSettlementStatus(Long orderId, int settlementStatus, Integer rebateStatus)
```

#### 智能选择更新方式
```java
if (rebateStatus != null) {
    // 同时更新结算状态和返佣状态
    updateResult = deliveryOrderMapper.updateSettlementAndRebateStatus(orderId, settlementStatus, rebateStatus);
} else {
    // 仅更新结算状态
    updateResult = deliveryOrderMapper.updateSettlementStatus(orderId, settlementStatus);
}
```

### 3. **修改调用方式**

#### 带单员订单结算
```java
// 修改前
updateOrderSettlementStatus(leaderOrder.getId(), 2);

// 修改后
updateOrderSettlementStatus(leaderOrder.getId(), 2, 2);  // 结算状态=2（已结算），返佣状态=2（已返）
```

#### 跟单员订单结算
```java
// 修改前
updateOrderSettlementStatus(followOrder.getId(), 2);
updateRebateStatusByProfit(followOrder);

// 修改后
int rebateStatus = (profit.compareTo(BigDecimal.ZERO) >= 0) ? 2 : 1;  // 盈利或持平=2（已返），亏损=1（未返）
updateOrderSettlementStatus(followOrder.getId(), 2, rebateStatus);
```

## 📊 **状态对应关系**

### 结算状态 (is_settlement)
- `0` = 未结算
- `1` = 待结算  
- `2` = 已结算

### 返佣状态 (rebate_status)
- `0` = 初始状态
- `1` = 未返
- `2` = 已返

### 更新规则
| 订单类型 | 盈亏情况 | 结算状态 | 返佣状态 |
|---------|---------|----------|----------|
| 带单员订单 | 盈利 | 2（已结算） | 2（已返） |
| 带单员订单 | 亏损 | 2（已结算） | 2（已返） |
| 跟单员订单 | 盈利/持平 | 2（已结算） | 2（已返） |
| 跟单员订单 | 亏损 | 2（已结算） | 1（未返） |

## 🔍 **验证机制**

### 双重验证
```java
// 验证结算状态
if (updatedOrder.getIsSettlement() != settlementStatus) {
    throw new RuntimeException("结算状态更新验证失败");
}

// 验证返佣状态
if (updatedOrder.getRebateStatus() != rebateStatus) {
    throw new RuntimeException("返佣状态更新验证失败");
}
```

### 详细日志
```java
log.info("🎯 结算状态和返佣状态更新验证成功 - 订单ID: {}, 结算状态: {}, 返佣状态: {}", 
        orderId, settlementStatus, rebateStatus);
```

## ✅ **预期效果**

### 修改前的问题
- 结算状态更新成功，但返佣状态可能不同步
- 需要额外的步骤来更新返佣状态
- 可能出现状态不一致的情况

### 修改后的改进
- **原子性操作**：一次SQL同时更新两个状态
- **状态一致性**：确保结算状态和返佣状态同步更新
- **简化流程**：减少额外的状态更新步骤
- **增强验证**：双重验证确保更新成功

## 🧪 **测试验证**

### 测试场景
1. **带单员盈利订单**：结算状态=2，返佣状态=2
2. **带单员亏损订单**：结算状态=2，返佣状态=2
3. **跟单员盈利订单**：结算状态=2，返佣状态=2
4. **跟单员亏损订单**：结算状态=2，返佣状态=1

### 验证SQL
```sql
-- 查看订单状态
SELECT id, user_id, leader_id, profit, is_settlement, rebate_status, status 
FROM delivery_order 
WHERE id IN (订单ID列表)
ORDER BY id;
```

### 预期日志
```
🎯 结算状态和返佣状态更新验证成功 - 订单ID: XX, 结算状态: 2, 返佣状态: 2
```

## 📁 **修改的文件**

1. **DeliveryOrderMapper.java**
   - 新增 `updateSettlementAndRebateStatus` 方法

2. **SettlementServiceImpl.java**
   - 重载 `updateOrderSettlementStatus` 方法
   - 修改带单员订单结算调用
   - 修改跟单员订单结算调用
   - 增强验证和日志

## 🔄 **向后兼容性**

- 保留了原有的 `updateOrderSettlementStatus(Long orderId, int settlementStatus)` 方法
- 新方法通过重载实现，不影响现有调用
- 可以根据需要选择是否同时更新返佣状态

## 🎉 **总结**

这次修改实现了在订单结算完成时同时更新返佣状态的功能，确保了数据的一致性和完整性。通过原子性操作和双重验证，大大提高了系统的可靠性。
