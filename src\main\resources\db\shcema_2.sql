-- 创建数据库
DROP DATABASE IF EXISTS trading_system;
CREATE DATABASE trading_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE trading_system;

-- 系统用户表
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
    `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
    `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
    `avatar` varchar(100) DEFAULT NULL COMMENT '头像',
    `status` tinyint(4) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 初始化系统管理员
INSERT INTO `sys_user` VALUES
                           (1, 'admin', '$2a$10$RZLZiVE8wJXmQpTE/PS91ed9NTJQv2gZ6hZbz7nurxdh9QBn/QE.W', '管理员', '13800138000', '<EMAIL>', NULL, 1, NOW(), NOW());


-- 角色表
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_name` varchar(50) NOT NULL COMMENT '角色名称',
    `role_key` varchar(50) NOT NULL COMMENT '角色权限字符串',
    `role_sort` int(4) DEFAULT '0' COMMENT '显示顺序',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:停用,1:正常)',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 初始化角色
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, 1, '超级管理员', NOW(), NOW());
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, 1, '普通角色', NOW(), NOW());

-- 用户和角色关联表
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 初始化用户和角色关联关系
INSERT INTO `sys_user_role` VALUES (1, 1);

-- 菜单权限表
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
    `parent_id` bigint(20) DEFAULT '0' COMMENT '父菜单ID',
    `order_num` int(4) DEFAULT '0' COMMENT '显示顺序',
    `path` varchar(200) DEFAULT '' COMMENT '路由地址',
    `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
    `query_param` varchar(255) DEFAULT NULL COMMENT '路由参数',
    `is_frame` tinyint(1) DEFAULT '1' COMMENT '是否为外链（0是 1否）',
    `is_cache` tinyint(1) DEFAULT '0' COMMENT '是否缓存（0不缓存 1缓存）',
    `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
    `visible` tinyint(1) DEFAULT '1' COMMENT '菜单状态（0隐藏 1显示）',
    `status` tinyint(1) DEFAULT '1' COMMENT '菜单状态（0停用 1正常）',
    `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
    `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';
INSERT INTO `sys_menu` VALUES
-- 一级菜单
(1, '系统管理', 0, 1, 'system', NULL, NULL, 1, 0, 'M', 1, 1, '', 'setting', NOW(), NOW()),
(2, '用户管理', 0, 2, 'user', NULL, NULL, 1, 0, 'M', 1, 1, '', 'user', NOW(), NOW()),
(3, '财务管理', 0, 4, 'finance', NULL, NULL, 1, 0, 'M', 1, 1, '', 'money', NOW(), NOW()),
(4, '公告管理', 0, 5, 'notice', NULL, NULL, 1, 0, 'M', 1, 1, '', 'bell', NOW(), NOW()),
(5, '日志管理', 0, 6, 'log', NULL, NULL, 1, 0, 'M', 1, 1, '', 'document', NOW(), NOW()),
(6, '佣金管理', 0, 7, 'reward', NULL, NULL, 1, 0, 'M', 1, 1, '', 'present', NOW(), NOW()),
(7, '轮播管理', 0, 8, 'carousel', NULL, NULL, 1, 0, 'M', 1, 1, '', 'picture', NOW(), NOW()),
(8, '定时任务', 0, 9, 'task', NULL, NULL, 1, 0, 'M', 1, 1, '', 'timer', NOW(), NOW()),
(9, '平台介绍', 0, 10, 'system/agreement', NULL, NULL, 1, 0, 'M', 1, 1, '', 'document', NOW(), NOW()),
(10, '交易所管理', 0, 3, 'exchange', NULL, NULL, 1, 0, 'M', 1, 1, '', 'money', NOW(), NOW()),

-- 系统管理子菜单
(100, '用户管理', 1, 1, 'user', 'dashboard/user', NULL, 1, 0, 'C', 1, 1, 'system:user:list', 'user', NOW(), NOW()),
(101, '角色管理', 1, 2, 'role', 'dashboard/role', NULL, 1, 0, 'C', 1, 1, 'system:role:list', 'peoples', NOW(), NOW()),
-- (102, '菜单管理', 1, 3, 'menu', 'dashboard/menu', NULL, 1, 0, 'C', 1, 1, 'system:menu:list', 'tree-table', NOW(), NOW()),
(103, '参数设置', 1, 4, 'params', 'dashboard/settings/params', NULL, 1, 0, 'C', 1, 1, 'system:params:list', 'edit', NOW(), NOW()),

-- 用户管理子菜单
(200, '用户列表', 2, 1, 'list', 'dashboard/user/list', NULL, 1, 0, 'C', 1, 1, 'user:list:list', 'user', NOW(), NOW()),
-- 用户钱包地址
(201, '钱包地址', 2, 2, 'wallet', 'dashboard/user/wallet', NULL, 1, 0, 'C', 1, 1, 'user:wallet:list', 'wallet', NOW(), NOW()),
(203, '拓扑图', 2, 4, 'topology', 'dashboard/user/topology', NULL, 1, 0, 'C', 1, 1, 'user:topology:list', 'tree', NOW(), NOW()),

-- 财务管理子菜单
(300, '充值明细', 3, 1, 'recharge-record', 'dashboard/finance/recharge-record', NULL, 1, 0, 'C', 1, 1, 'finance:recharge:list', 'money', NOW(), NOW()),

(302, '转账明细', 3, 3, 'transfer-record', 'dashboard/finance/transfer-record', NULL, 1, 0, 'C', 1, 1, 'finance:transfer:list', 'transfer', NOW(), NOW()),
(303, '提现明细', 3, 4, 'withdraw-record', 'dashboard/finance/withdraw-record', NULL, 1, 0, 'C', 1, 1, 'finance:withdraw:list', 'pay', NOW(), NOW()),
 
-- 公告管理子菜单
(400, '公告列表', 4, 1, 'list', 'dashboard/notice/list', NULL, 1, 0, 'C', 1, 1, 'notice:list:list', 'list', NOW(), NOW()),
(401, '发布公告', 4, 2, 'publish', 'dashboard/notice/publish', NULL, 1, 0, 'C', 1, 1, 'notice:publish:add', 'edit', NOW(), NOW()),
(402, '首页通知', 4, 3, 'home', 'dashboard/notice/home', NULL, 1, 0, 'C', 1, 1, 'notice:home:list', 'bell', NOW(), NOW()),

-- 日志管理子菜单
(500, '操作日志', 5, 1, 'operation', 'dashboard/log/operation', NULL, 1, 0, 'C', 1, 1, 'log:operation:list', 'form', NOW(), NOW()),
(501, '登录日志', 5, 2, 'login', 'dashboard/log/login', NULL, 1, 0, 'C', 1, 1, 'log:login:list', 'logininfor', NOW(), NOW()),
(502, '任务日志', 5, 3, 'task', 'dashboard/log/task', NULL, 1, 0, 'C', 1, 1, 'log:task:list', 'job', NOW(), NOW()),

-- 佣金管理子菜单
(600, '赠送列表', 6, 1, 'list', 'dashboard/reward/list', NULL, 1, 0, 'C', 1, 1, 'reward:list:list', 'money', NOW(), NOW()),

-- 轮播管理子菜单
(700, '轮播列表', 7, 1, 'list', 'dashboard/carousel/list', NULL, 1, 0, 'C', 1, 1, 'carousel:list:list', 'list', NOW(), NOW()),

-- 定时任务子菜单
(800, '任务列表', 8, 1, 'list', 'dashboard/task/list', NULL, 1, 0, 'C', 1, 1, 'task:list:list', 'job', NOW(), NOW()),

-- 法律条款子菜单
(900, '平台介绍', 9, 1, 'list', 'dashboard/system/agreement', NULL, 1, 0, 'C', 1, 1, 'agreement:list:list', 'documentation', NOW(), NOW()),

-- 交易所管理子菜单
(1000, '交易对列表', 10, 1, 'pairs', 'dashboard/exchange/pairs', NULL, 1, 0, 'C', 1, 1, 'exchange:pairs:list', 'money', NOW(), NOW()),
-- 期权订单（属于交易所管理）
(1001, '期权订单', 10, 2, 'options', 'dashboard/exchange/options', NULL, 1, 0, 'C', 1, 1, 'exchange:options:list', 'money', NOW(), NOW()),
-- 带单管理属于交易所管理）
(1002, '带单管理', 10, 3, 'copy-trade', 'dashboard/exchange/copy-trade', NULL, 1, 0, 'C', 1, 1, 'exchange:copy-trade:list', 'money', NOW(), NOW()),
 -- 划转明细属于交易所管理）
(1003, '划转明细', 10, 6, 'transfer', 'dashboard/exchange/transfer', NULL, 1, 0, 'C', 1, 1, 'exchange:transfer:list', 'money', NOW(), NOW()),
-- 账户交易明细属于交易所管理）
(1004, '账户交易明细', 10, 7, 'trade', 'dashboard/exchange/trade', NULL, 1, 0, 'C', 1, 1, 'exchange:trade:list', 'money', NOW(), NOW());

-- 角色和菜关联表
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu` (
                                 `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                 `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                 PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和菜单关联表';

-- 初始化角色菜单关联关系(超级管理员拥有所有权限)
INSERT INTO `sys_role_menu`
SELECT 1, id FROM sys_menu;


-- 交易所板块 
DROP TABLE IF EXISTS `exchange_pair_info`;
CREATE TABLE `exchange_pair_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `exchange_name` varchar(50) NOT NULL COMMENT '交易所名称',
    `token_name` varchar(50) NOT NULL COMMENT '代币名称',
    `pair_name` varchar(50) NOT NULL COMMENT '交易对名称',
    `logo_url` varchar(255) COMMENT 'logo地址',
    `api_url` varchar(255) COMMENT 'api请求地址',
    `special_processing` tinyint(1) DEFAULT '0' COMMENT '特殊处理(0:否,1:是)',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用(0:禁用,1:启用)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',        
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易所板块表';

-- 初始化交易对数据
INSERT INTO `exchange_pair_info` 
(`exchange_name`, `token_name`, `pair_name`, `logo_url`, `api_url`, `sort`, `is_enabled`, `create_time`, `update_time`, `special_processing`)    
VALUES 
('Binance', '比特币', 'BTCUSDT', 'https://www.binance.com/static/images/coin/btc.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT', 1, 1, NOW(), NOW(), 0),
('Binance', '以太坊', 'ETHUSDT', 'https://www.binance.com/static/images/coin/eth.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=ETHUSDT', 2, 1, NOW(), NOW(), 0),
('Binance', '波场', 'TRXUSDT', 'https://www.binance.com/static/images/coin/trx.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=TRXUSDT', 3, 1, NOW(), NOW(), 0),    
('Binance', '狗狗币', 'DOGEUSDT', 'https://www.binance.com/static/images/coin/doge.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=DOGEUSDT',4, 1, NOW(), NOW(), 0),
('Binance', '莱特币', 'LTCUSDT', 'https://www.binance.com/static/images/coin/ltc.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=LTCUSDT', 5, 1, NOW(), NOW(), 0),
('Binance', 'Solana', 'SOLUSDT', 'https://www.binance.com/static/images/coin/sol.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=SOLUSDT',6, 1, NOW(), NOW(), 0),   
('Binance', 'Sui', 'SUIUSDT', 'https://www.binance.com/static/images/coin/sui.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=SUIUSDT', 7, 1, NOW(), NOW(), 0),
('Binance', 'Pepe', 'PEPEUSDT', 'https://www.binance.com/static/images/coin/pepe.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=PEPEUSDT',8, 1, NOW(), NOW(), 0),
('Binance', 'Ordinals', 'ORDIUSDT', 'https://www.binance.com/static/images/coin/ordinals.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=ORDIUSDT',9, 1, NOW(), NOW(), 0),
('Binance', 'Polkadot', 'DOTUSDT', 'https://www.binance.com/static/images/coin/dot.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=DOTUSDT', 10, 1, NOW(), NOW(), 0),
('Binance', 'Shiba Inu', 'SHIBUSDT', 'https://www.binance.com/static/images/coin/shib.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=SHIBUSDT', 11, 1, NOW(), NOW(), 0),
('Binance', 'Toncoin', 'TONUSDT', 'https://www.binance.com/static/images/coin/ton.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=TONUSDT', 12, 1, NOW(), NOW(), 0),
('Binance', 'Ripple', 'XRPUSDT', 'https://www.binance.com/static/images/coin/xrp.png', 'https://api.binance.com/api/v3/ticker/24hr?symbol=XRPUSDT', 13, 1, NOW(), NOW(), 0);    

-- 完整的front_user表创建语句(包含新字段)
DROP TABLE IF EXISTS `front_user`;
CREATE TABLE `front_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `user_no` varchar(32) NOT NULL COMMENT '用户编号',
    `username` varchar(50) DEFAULT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `email` varchar(500) DEFAULT NULL COMMENT '电子邮件',
    `security_password` varchar(100) DEFAULT NULL COMMENT '安全密码',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `phone` varchar(11) DEFAULT NULL COMMENT '手机号',
    `share_code` varchar(20) NOT NULL COMMENT '分享码',
    `referrer_code` varchar(20) DEFAULT NULL COMMENT '推荐人分享码',
    `agent_level` bigint(20) DEFAULT '0' COMMENT '代理等级ID',                            
    `available_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '资金账户',
    `copy_trade_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '跟单账户',
    `commission_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '佣金账户',
    `profit_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '利润账户',
    `usage_frozen_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '使用冻结账户',
    `frozen_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '提现冻结余额',
    `cat_balance` decimal(30,4) DEFAULT '0.0000' COMMENT 'CAT币',
    `team_total_count` int(11) DEFAULT '0' COMMENT '团队总账户数',
    `team_today_count` int(11) DEFAULT '0' COMMENT '团队今日新增账户数',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
    `commission_rate` decimal(20,2) DEFAULT '0.00' COMMENT '佣金比例',
    `total_recharge` decimal(30,4) DEFAULT '0.0000' COMMENT '累积充值',
    `is_activated` tinyint(1) DEFAULT '0' COMMENT '是否激活(0:未激活,1:已激活)',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',                             
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_no` (`user_no`),                              
    UNIQUE KEY `uk_share_code` (`share_code`),
    KEY `idx_referrer_code` (`referrer_code`),
    KEY `idx_referrer_share_code` (`referrer_code`, `share_code`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='前台用户表';

-- 完整的初始化数据
INSERT INTO `front_user`
(
  `id`, `user_no`, `username`, `password`, `email`, `security_password`, `real_name`, `phone`, `share_code`,
  `referrer_code`, `agent_level`, `available_balance`, `copy_trade_balance`, `commission_balance`,
  `usage_frozen_balance`, `profit_balance`, `frozen_balance`, `team_total_count`, `team_today_count`,
  `status`, `commission_rate`, `total_recharge`, `is_activated`, `create_time` 
)
VALUES
(
  1, '********', 'agent001', '$2a$10$RZLZiVE8wJXmQpTE/PS91ed9NTJQv2gZ6hZbz7nurxdh9QBn/QE.W',
  '<EMAIL>', '$2a$10$RZLZiVE8wJXmQpTE/PS91ed9NTJQv2gZ6hZbz7nurxdh9QBn/QE.W', '张三', '***********', 'SHARE001',
  NULL, 1, 0.0000, 0.0000, 0.0000,
  0.0000, 0.0000, 0.0000, 0, 0,
  1, 100, 0.0000, 0, '2024-01-01 10:00:00'      
);


-- 用户钱包提现地址表
-- DROP TABLE IF EXISTS `user_bank_card`;
-- CREATE TABLE `user_bank_card` (
--     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
--     `user_id` bigint(20) NOT NULL COMMENT '用户ID',
--     `address` varchar(500) NOT NULL COMMENT '钱包地址',
--     `chain_name` varchar(50) NOT NULL COMMENT '链名称',
--     `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认提现地址(0:否,1:是)',
--     `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
--     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     PRIMARY KEY (`id`),
--     KEY `idx_user_id` (`user_id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户银行卡表';

-- 新建一个用户钱包地址管理表，id ,属于那个链，链地址,私钥，用户id,创建时间,更新时间
DROP TABLE IF EXISTS `user_wallet_address`;
CREATE TABLE `user_wallet_address` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',  
    `chain_name` varchar(255) NOT NULL COMMENT '链名称',  
    `chain_address` varchar(500) NOT NULL COMMENT '链地址',    
    `private_key` varchar(1000) NOT NULL COMMENT '私钥', 
    `bnb_balance` decimal(40,7) DEFAULT '0.0000000000' COMMENT '账户bnb余额',
    `usdt_balance` decimal(40,7) DEFAULT '0.0000000000' COMMENT '账户usdt余额',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',  
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包地址管理表';

-- 初始化钱包地址测试数据
INSERT INTO `user_wallet_address` 
(`chain_name`, `chain_address`, `private_key`, `bnb_balance`, `usdt_balance`, `user_id`, `create_time`) 
VALUES
('BSC', '******************************************', '0xaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa', 0.1234567, 1000.0000000, 1, '2024-01-20 10:00:00'),
('BSC', '******************************************', '0xbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb', 0.2345678, 2000.0000000, 1, '2024-01-20 10:01:00'),
('BSC', '******************************************', '0xcccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc', 0.3456789, 3000.0000000, 1, '2024-01-20 10:02:00'),
('BSC', '******************************************', '0xdddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd', 0.4567890, 4000.0000000, 1, '2024-01-20 10:03:00'),
('BSC', '******************************************', '0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', 0.5678901, 5000.0000000, 1, '2024-01-20 10:04:00');

-- 系统参数配置表
DROP TABLE IF EXISTS `sys_params`;
CREATE TABLE `sys_params` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参数ID',
    `min_transfer` decimal(10,2) DEFAULT '100.00' COMMENT '最低转账限额',
    `max_transfer` decimal(10,2) DEFAULT '50000.00' COMMENT '最高转账限额',
    `transfer_fee` decimal(5,2) DEFAULT '0' COMMENT '转账手续费',
    `enable_transfer` tinyint(1) DEFAULT '1' COMMENT '是否允许转账(0:禁用,1:启用)',
    `min_withdraw` decimal(10,2) DEFAULT '100.00' COMMENT '最低提现限额',
    `max_withdraw` decimal(10,2) DEFAULT '50000.00' COMMENT '最高提现限额',
    `max_auto_withdraw` decimal(10,2) DEFAULT '200.00' COMMENT '提现最高自动转账',
    `withdraw_fee` decimal(5,2) DEFAULT '1.00' COMMENT '提现手续费',
    `enable_withdraw` tinyint(1) DEFAULT '1' COMMENT '是否允许提现(0:禁用,1:启用)', 
    `auto_withdraw` tinyint(1) DEFAULT '1' COMMENT '是否自动提现 0-关闭 1-开启',
    `enable_internal_transfer` tinyint(1) DEFAULT '1' COMMENT '是否允许内部转账 0-关闭 1-开启',                            
    `min_copy_trade` decimal(10,2) DEFAULT '900.00' COMMENT '最低跟单额度',
    `trade_profit_rate` decimal(5,2) DEFAULT '50' COMMENT '交易盈利比例',
    `copy_trade_account_rate` decimal(5,2) DEFAULT '5.00' COMMENT '每次跟单的跟单账户比例',
    `copy_trade_fee` decimal(5,2) DEFAULT '20' COMMENT '每笔跟单手续费比例',
    `platform_fee_rate` decimal(5,2) DEFAULT '40' COMMENT '平台预留手续费比例（剩余走极差给用户）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统参数配置表';

-- 充值明细表
DROP TABLE IF EXISTS `recharge_record`;
CREATE TABLE `recharge_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名称',
    `email` varchar(255) NOT NULL COMMENT '邮箱',
    `amount` decimal(12,2) NOT NULL COMMENT '充值金额',
    `recharge_type` tinyint(1) NOT NULL COMMENT '充值类型(1:链上充值,2:后台充值)',
    `audit_status` tinyint(1) DEFAULT '0' COMMENT '审核状态(0:待审核,1:已通过,2:已拒绝)',
    `proof_image` varchar(255) DEFAULT NULL COMMENT '充值凭证图片',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注说明',
    `tx_hash` varchar(80) DEFAULT NULL COMMENT '链上充值交易哈希',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '充值时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_audit_status` (`audit_status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_tx_hash` (`tx_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值明细表';



 


-- 转账明细表
DROP TABLE IF EXISTS `transfer_record`;
CREATE TABLE `transfer_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `from_user_id` bigint(20) NOT NULL COMMENT '转出用户ID',
    `from_username` varchar(50) NOT NULL COMMENT '转出用户名',
    `to_user_id` bigint(20) NOT NULL COMMENT '转入用户ID',
    `to_username` varchar(50) NOT NULL COMMENT '转入用户名',
    `amount` decimal(12,2) NOT NULL COMMENT '转账金额',
    `fee` decimal(12,2) NOT NULL COMMENT '手续费',
    `real_amount` decimal(12,2) NOT NULL COMMENT '实际到账金额',
    `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:处理中,1:成功,2:失败)',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_from_user_id` (`from_user_id`),
    KEY `idx_to_user_id` (`to_user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转账明细表';



-- 提现明细表
DROP TABLE IF EXISTS `withdraw_record`;
CREATE TABLE `withdraw_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `register_email` varchar(100) NOT NULL COMMENT '注册邮箱',
    `amount` decimal(12,2) NOT NULL COMMENT '提现金额',
    `fee` decimal(12,2) NOT NULL COMMENT '手续费',
    `real_amount` decimal(12,2) NOT NULL COMMENT '实际到账金额',
    `address` varchar(500) NOT NULL COMMENT '提现地址',
    `chain_name` varchar(50) NOT NULL COMMENT '链名称',
    `tx_hash` varchar(100) DEFAULT NULL COMMENT '链上转账哈希',
    `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:待审核,1:已通过,2:拒绝,3:审核失  )',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_register_email` (`register_email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现明细表';

 



-- 公告表
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
    `title` varchar(100) NOT NULL COMMENT '公告标题',
    `content` text NOT NULL COMMENT '公告内容',
    `notice_type` tinyint(1) DEFAULT '1' COMMENT '公告类型(1:重要,2:通知,3:系统,4:活动,5:维护,6.通知)',
    `sort` int(11) DEFAULT '0' COMMENT '排序号',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
    `is_top` tinyint(1) DEFAULT '0' COMMENT '是否置顶(0:否,1:是)',
    `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

-- 初始化  告测试数据
INSERT INTO `notice`
(`title`, `content`, `notice_type`, `sort`, `status`, `is_top`, `publish_time`, `create_by`, `create_time`)
VALUES
    ('系统维护通知', '尊敬的用户：

系统将于2024年1月20日晚上22:00-24:00进行系统维护升级，期间系统将暂停服务，请提前做好相关准备。给您带来的不便敬请谅解！

维护内容：
1. 系统性能优化
2. 安全性升级
3. 新功能上线准备

注意事项：
1. 请您在维护前保存重要数据
2. 维护期间可能无法访问系统
3. 如有疑问请联系客服

系统管理团队', 1, 1, 1, 1, '2024-01-19 10:00:00', 'admin', '2024-01-19 10:00:00'),

    ('新版功能上线通知', '尊敬的用户：

系统新版本已于2024年1月18日正式上线，新增多项实用功能，欢迎体验！

更新内容：
1. 全新的用户界面
2. 更快的响应速度
3. 更多的操作选项

欢迎反馈使用意见！', 2, 2, 1, 0, '2024-01-18 10:00:00', 'admin', '2024-01-18 10:00:00'),

    ('春节活动预告', '春节期间邀请好友注册即可获得88元现金奖励！

活动时间：2024年2月1日至2024年2月20日
活动规则：
1. 邀请新用户注册并认证
2. 新用户完成首次设备购买
3. 双方各获得88元现金奖励

机会难得，赶快行动吧！', 4, 3, 1, 1, '2024-01-20 10:00:00', 'admin', '2024-01-20 10:00:00');

-- 首页通知表
DROP TABLE IF EXISTS `home_notice`;
CREATE TABLE `home_notice` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
    `content` text NOT NULL COMMENT '通知内容',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:关闭,1:开启)',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页通知表';

-- 初始化首页通知测试数据
INSERT INTO `home_notice`
(`content`, `status`, `create_by`, `create_time`)
VALUES
    ('欢迎使用本系统！\n1. 新用户注册  送88元现金奖励\n2. 邀请好友最高可得1888元奖励\n3. 平台每日签到可获得积分奖励', 1, 'admin', '2024-01-20 10:00:00');


-- 操作日志表
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
    `title` varchar(50) DEFAULT '' COMMENT '模块标题',
    `oper_type` varchar(20) DEFAULT '' COMMENT '操作类型（新增、修改,删除,查询,导出，其他）',
    `method` varchar(100) DEFAULT '' COMMENT '方法名称',
    `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
    `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
    `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
    `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
    `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
    `status` tinyint(1) DEFAULT '0' COMMENT '操作状态（0失败 1成功）',
    `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
    `oper_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_oper_time` (`oper_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';


-- 登录日志表
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
    `username` varchar(50) NOT NULL COMMENT '用户账号',
    `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
    `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
    `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
    `os` varchar(50) DEFAULT '' COMMENT '操作系统',
    `status` tinyint(1) DEFAULT '0' COMMENT '登录状态0失败 1成功）',
    `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
    `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_status` (`status`),
    KEY `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统登录日志';



-- 任务执行日志表
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `job_id` bigint(20) NOT NULL COMMENT '任务ID',
    `job_name` varchar(64) NOT NULL COMMENT '任务名称',
    `job_type` varchar(20) NOT NULL COMMENT '任务类型(SYSTEM:系统任务,MONITOR:监控任务,BACKUP:备份任务)',
    `execution_time` datetime NOT NULL COMMENT '执行时间',
    `execution_duration` int(11) NOT NULL COMMENT '耗时(ms)',
    `execution_result` varchar(20) NOT NULL COMMENT '执行结果',
    `execution_message` varchar(1000) DEFAULT NULL COMMENT '执行消息',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_execution_time` (`execution_time`),
    KEY `idx_job_type` (`job_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务执行日志表';


-- 任务表
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `job_name` varchar(64) NOT NULL COMMENT '任务名称',
    `job_type` varchar(20) NOT NULL COMMENT '任务类型(SYSTEM:系统任务,MONITOR:监控任务,BACKUP:备份任务)',
    `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
    `cron_expression` varchar(255) NOT NULL COMMENT 'cron执行表达式',
    `misfire_policy` tinyint(1) DEFAULT '3' COMMENT '执行策略(1:立即执行,2:执行一次,3:放弃执行)',
    `concurrent` tinyint(1) DEFAULT '0' COMMENT '是否并发执行(0:允许,1:禁止)',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:暂停,1:正常)',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务表';

-- 初始化任务列表测试数据
INSERT INTO `sys_job`
(`job_name`, `job_type`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `remark`, `create_by`, `create_time`)
VALUES
-- 每天零点重置用户团队今日统计
('用户统计重置任务', 'SYSTEM', 'systemTask.resetTeamCount', '0 0 0 * * ?', 3, 1, 1, '每天零点重置用户团队今日统计', 'admin', NOW()),
-- 添加设备到期检测任务：每天执行一次
('用户业绩统计任务', 'DEFAULT', 'systemTask.updateUserPerformance', '0 0 1 * * ?', 3, 1, 1, '每天凌晨1点执行用户业绩统计', 'admin', NOW()),
-- 添加用户钱包余额同步任务：每5分钟执行一次
('用户钱包余额同步任务', 'DEFAULT', 'systemTask.syncWalletBalances', '0 */5 * * * ?', 3, 1, 1, '每5分钟执行一次用户钱包余额同步', 'admin', NOW()),
-- 添加历史数据清理任务
('历史数据清理任务', 'SYSTEM', 'systemTask.cleanHistoricalData', '0 0 1 * * ?', 3, 1, 1, '每天凌晨1点执行历史数据清理', 'admin', NOW());


-- 赠送记录表
DROP TABLE IF EXISTS `commission_record`;
CREATE TABLE `commission_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名称',
    `phone` varchar(255) NOT NULL COMMENT '邮件',
    `commission_type` tinyint(1) DEFAULT '0' COMMENT '赠送类型(1.佣金奖励/2.收益奖励)',
    `commission_amount` decimal(12,2) NOT NULL COMMENT '金额',
    `release_status` tinyint(1) DEFAULT '0' COMMENT '状态(0:待赠送,1:已赠送)',
    `release_time` datetime DEFAULT NULL COMMENT '获得时间',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_release_status` (`release_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='赠送记录表';


-- 轮播图表
DROP TABLE IF EXISTS `sys_banner`;
CREATE TABLE `sys_banner` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '轮播ID',
    `title` varchar(100) NOT NULL COMMENT '轮播标题',
    `image_url` varchar(255) NOT NULL COMMENT '轮播图片',
    `jump_url` varchar(255) DEFAULT NULL COMMENT '跳转链接',
    `sort` int(11) DEFAULT '0' COMMENT '排序号',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播播表';



-- 平台介绍表
DROP TABLE IF EXISTS `sys_agreement`;
CREATE TABLE `sys_agreement` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条款ID',
    `title` varchar(100) NOT NULL COMMENT '标题名称',
    `content` text NOT NULL COMMENT '内容',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台介绍表';

-- 用户服务协议
INSERT INTO `sys_agreement`
(`title`, `content`, `status`, `create_by`, `create_time`)
VALUES
    (' 这是平台介绍', '这里写内容 ', 1, 'admin', '2024-01-20 10:00:00');
 

-- 验证码表
DROP TABLE IF EXISTS `sys_captcha`;
CREATE TABLE `sys_captcha` (
    `uuid` varchar(36) NOT NULL COMMENT '验证码统一标识',
    `code` varchar(4) NOT NULL COMMENT '验证码',
    `expire_time` datetime NOT NULL COMMENT '过期时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`uuid`),
    KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统验证码';
 

-- 带单人表
DROP TABLE IF EXISTS `copy_leader`;
CREATE TABLE `copy_leader` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `leader_uid` bigint(20) NOT NULL COMMENT '带单人用户ID',
    `leader_nickname` varchar(50) NOT NULL COMMENT '带单人昵称',
    `leader_avatar` varchar(255) DEFAULT NULL COMMENT '带单人头像',
    `symbol` varchar(50) NOT NULL COMMENT '当前带单交易对',
    `current_price` decimal(20,8) DEFAULT '0.00000000' COMMENT '当前交易对的价格(每次开始的时候就会更新这个价格，结束后就会清0)',
    `period_no` varchar(20) NOT NULL DEFAULT '' COMMENT '期号(如20240607-01)',
    `start_time` datetime NOT NULL COMMENT '跟单开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '跟单结束时间',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态(0未开始 1准备中 2已开始 3结算中 )',
    `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(1开启 0关闭)',  -- 新增
    `total_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '历史累计收益',
    `win_rate` decimal(8,2) DEFAULT '0.00' COMMENT '胜率(%)',
    `follower_count` int(11) DEFAULT '0' COMMENT '累计跟单人数',
    `current_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '本次带单总收益',
    `profit_rate` decimal(8,2) DEFAULT '0.00' COMMENT '本次带单收益率(%)',
    `margin_balance` decimal(20,8) DEFAULT '900000' COMMENT '保证金余额',
    `win_or_lose` tinyint(4) DEFAULT '0' COMMENT '做多还是做空(0做多 1做空)',
    `remark` varchar(255) DEFAULT NULL COMMENT '策略说明',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_leader_uid` (`leader_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='带单人表';

-- 跟单明细表
DROP TABLE IF EXISTS `copy_follow_detail`;
CREATE TABLE `copy_follow_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `leader_id` bigint(20) NOT NULL COMMENT '带单表id（外键）',
    `period_no` varchar(20)  NULL DEFAULT '' COMMENT '期号(如20240607-01)',
    `follower_uid` bigint(20) NOT NULL COMMENT '跟单人用户ID',
    `follower_nickname` varchar(50)  NULL COMMENT '跟单人昵称',
    `follower_avatar` varchar(255) DEFAULT NULL COMMENT '跟单人头像',
    `follow_amount` decimal(20,8) DEFAULT '0.00000000' COMMENT '跟单金额',
    `follow_time` datetime DEFAULT NULL COMMENT '跟单时间',
    `status` tinyint(4) DEFAULT '0' COMMENT '跟单状态(0未开始 1准备中 2已开始 3结算中 4已结束)',
    `is_following` tinyint(4) DEFAULT '0' COMMENT '是否一键跟单(0否 1是)',
    `result_status` tinyint(4) DEFAULT '0' COMMENT '结算结果(0未结算 1盈利 2亏损)',
    `is_returned` tinyint(4) DEFAULT '0' COMMENT '是否返本(0否 1是)',
    `real_time_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '实时收益',
    `final_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '最终收益',
    `profit_rate` decimal(8,2) DEFAULT '0.00' COMMENT '收益率(%)',
    `is_settled` tinyint(4) DEFAULT '0' COMMENT '是否已结算(0否 1是)',
    `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_leader_id` (`leader_id`),
    KEY `idx_follower_uid` (`follower_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跟单明细表';

-- 跟单历史表
DROP TABLE IF EXISTS `copy_follow_history`;
CREATE TABLE `copy_follow_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `leader_id` bigint(20) NOT NULL COMMENT '带单表id',
    `period_no` varchar(20) NOT NULL DEFAULT '' COMMENT '期号(如20240607-01)',
    `follower_uid` bigint(20) NOT NULL COMMENT '跟单人用户ID',
    `symbol` varchar(50) NOT NULL COMMENT '跟单交易对',
    `profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '盈亏',
    `profit_rate` decimal(8,2) DEFAULT '0.00' COMMENT '收益率',
    `result_status` tinyint(4) DEFAULT '0' COMMENT '结算结果(0未结算 1盈利 2亏损)',
    `is_returned` tinyint(4) DEFAULT '0' COMMENT '是否返本(0否 1是)',
    `follow_time` datetime NOT NULL COMMENT '跟单时间',
    `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
    PRIMARY KEY (`id`),
    KEY `idx_leader_id` (`leader_id`),
    KEY `idx_follower_uid` (`follower_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跟单历史表';

-- 带单人表默认数据
INSERT INTO `copy_leader` (
  `leader_uid`, `leader_nickname`, `leader_avatar`, `symbol`, `period_no`, `start_time`, `end_time`, `status`,
  `total_profit`, `win_rate`, `follower_count`, `current_profit`, `profit_rate`, `remark`
) VALUES
(1, '币圈大佬', '/static/avatar.png', 'BTC/USDT', '20240607-01', '2024-06-07 10:00:00', '2024-06-07 18:00:00', 1,
  12345.67, 85.2, 321, 234.56, 8.5, '主攻BTC短线策略');

-- 跟单明细表默认数据
INSERT INTO `copy_follow_detail` (
  `leader_id`, `period_no`, `follower_uid`, `follower_nickname`, `follower_avatar`, `follow_amount`, `follow_time`, `status`,
  `result_status`, `is_returned`, `real_time_profit`, `final_profit`, `profit_rate`, `is_settled`, `settle_time`, `is_following`
) VALUES
(1, '20240607-01', 2, '小明', '/static/avatar2.png', 1000.00, '2024-06-07 10:05:00', 1, 0, 0, 32.10, 0.00, 3.21, 0, NULL, 0),
(1, '20240607-01', 3, '小红', '/static/avatar3.png', 2000.00, '2024-06-07 10:10:00', 1, 0, 0, -12.50, 0.00, -0.62, 0, NULL, 0),
(1, '20240607-01', 4, '小刚', '/static/avatar4.png', 1500.00, '2024-06-07 10:15:00', 1, 0, 0, 50.00, 0.00, 3.33, 0, NULL);

-- 跟单历史表默认数据
INSERT INTO `copy_follow_history` (
  `leader_id`, `period_no`, `follower_uid`, `symbol`, `profit`, `profit_rate`, `result_status`, `is_returned`, `follow_time`, `settle_time`
) VALUES
(1, '********-01', 2, 'BTC/USDT', 120.00, 12.0, 1, 1, '2024-06-06 09:00:00', '2024-06-06 18:00:00'),
(1, '********-01', 3, 'BTC/USDT', -50.00, -5.0, 2, 0, '2024-06-05 09:00:00', '2024-06-05 18:00:00'),
(1, '********-01', 4, 'BTC/USDT', 80.00, 8.0, 1, 1, '2024-06-04 09:00:00', '2024-06-04 18:00:00');


-- 账户划转记录表
DROP TABLE IF EXISTS `account_transfer_record`;
CREATE TABLE `account_transfer_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `from_account_type` varchar(20) NOT NULL COMMENT '转出账户类型(fund:资金账户,commission:佣金账户,copy:跟单账户,profit:利润账户)',
    `to_account_type` varchar(20) NOT NULL COMMENT '转入账户类型(fund:资金账户,commission:佣金账户,copy:跟单账户,profit:利润账户)',
    `amount` decimal(30,4) NOT NULL COMMENT '划转金额',
    `from_balance_before` decimal(30,4) NOT NULL COMMENT '转出前余额',
    `from_balance_after` decimal(30,4) NOT NULL COMMENT '转出后余额',
    `to_balance_before` decimal(30,4) NOT NULL COMMENT '转入前余额',
    `to_balance_after` decimal(30,4) NOT NULL COMMENT '转入后余额',
    `status` tinyint(4) DEFAULT '0' COMMENT '状态(0:处理中,1:成功,2:失败)',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_from_account_type` (`from_account_type`),
    KEY `idx_to_account_type` (`to_account_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户划转记录表';

-- 添加索引以提高查询性能
CREATE INDEX `idx_user_status_time` ON `account_transfer_record` (`user_id`, `status`, `create_time`);
CREATE INDEX `idx_account_types` ON `account_transfer_record` (`from_account_type`, `to_account_type`);
 
-- 交易明细表
DROP TABLE IF EXISTS `trade_record`;
CREATE TABLE `trade_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(255) NOT NULL COMMENT '用户名',
    `trade_type` varchar(50) NOT NULL COMMENT '交易类型',
    `amount` decimal(30,4) NOT NULL COMMENT '交易金额（可正可负）',
    `account_type` tinyint(4) NOT NULL COMMENT '归属账户(1:资金账户,2:跟单账户)',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_account_type` (`account_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易明细表';

-- 初始化 trade_record 交易明细表测试数据
INSERT INTO `trade_record`
(`user_id`, `username`, `trade_type`, `amount`, `account_type`, `remark`, `create_time`)
VALUES
(1, 'agent001', '充值', 10000.00, 1, '用户充值到账', '2024-06-01 10:00:00'),
(1, 'agent001', '提现', -2000.00, 1, '用户提现', '2024-06-02 11:00:00'),
(1, 'agent001', '跟单转入', 3000.00, 2, '资金账户划转到跟单账户', '2024-06-03 12:00:00'),
(1, 'agent001', '跟单收益', 500.00, 2, '跟单盈利结算', '2024-06-04 13:00:00'),
(1, 'agent001', '跟单亏损', -100.00, 2, '跟单亏损结算', '2024-06-05 14:00:00'),
(1, 'agent001', '资金返还', 1000.00, 1, '跟单结束返还本金', '2024-06-06 15:00:00');


-- 期权订单表（买涨买跌周期玩法）
DROP TABLE IF EXISTS `futures_option_order`;
CREATE TABLE `futures_option_order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `symbol` varchar(50) NOT NULL COMMENT '交易对（如BTC/USDT）',
    `direction` varchar(10) NOT NULL COMMENT '方向（up=买涨，down=买跌）',
    `amount` decimal(20,8) NOT NULL COMMENT '下单金额',
    `period` int(11) NOT NULL COMMENT '周期（秒）',
    `order_time` datetime NOT NULL COMMENT '下单时间',
    `open_price` decimal(20,8) NOT NULL COMMENT '开仓价（下单时价格）',
    `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
    `close_price` decimal(20,8) DEFAULT NULL COMMENT '结算价（到期时价格）',
    `profit` decimal(20,8) DEFAULT NULL COMMENT '盈亏金额（浮动收益）',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态（0=持仓中，1=已结算，2=结算中）',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_symbol` (`symbol`),
    KEY `idx_order_time` (`order_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期权订单表（买涨买跌周期玩法）';

-- 初始化期权订单表测试数据
INSERT INTO `futures_option_order`
(`user_id`, `symbol`, `direction`, `amount`, `period`, `order_time`, `open_price`, `settle_time`, `close_price`, `profit`, `status`, `remark`)
VALUES
(1, 'BTC/USDT', 'up',   100.00, 60, '2024-06-07 10:00:00', 10000.00, '2024-06-07 10:01:00', 10100.00, 1.00, 1, '买涨盈利'),
(1, 'BTC/USDT', 'down', 200.00, 120, '2024-06-07 11:00:00', 12000.00, '2024-06-07 11:02:00', 11900.00, 1.67, 1, '买跌盈利'),
(2, 'ETH/USDT', 'up',   150.00, 60, '2024-06-07 12:00:00', 3000.00, '2024-06-07 12:01:00', 2990.00, -0.50, 1, '买涨亏损'),
(2, 'ETH/USDT', 'down', 80.00,  180, '2024-06-07 13:00:00', 3100.00, NULL, NULL, NULL, 0, '持仓中'),
(3, 'BTC/USDT', 'up',   50.00,  60, '2024-06-07 14:00:00', 10500.00, '2024-06-07 14:01:00', 10400.00, -0.48, 1, '买涨亏损'),
(3, 'BTC/USDT', 'down', 60.00,  120, '2024-06-07 15:00:00', 10600.00, NULL, NULL, NULL, 0, '持仓中');

-- 持仓：SELECT * FROM futures_option_order WHERE user_id=? AND status=0
-- 盈利：SELECT * FROM futures_option_order WHERE user_id=? AND status=1 AND profit>0
-- 成交明细：SELECT * FROM futures_option_order WHERE user_id=? AND status=1


DROP TABLE IF EXISTS `chain_config`;
CREATE TABLE `chain_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `collect_address` varchar(64)  NOT NULL COMMENT '归集地址',
  `collect_balance` decimal(20, 8) NOT NULL DEFAULT 0.00000000 COMMENT '归集地址余额',
  `bnb_address` varchar(64)  NOT NULL COMMENT 'BNB管理地址',
  `bnb_balance` decimal(20, 8) NOT NULL DEFAULT 0.00000000 COMMENT 'BNB管理地址余额',
  `bnb_private_key` varchar(255)  NOT NULL COMMENT 'BNB管理地址私钥',
  `withdraw_address` varchar(64)  NOT NULL COMMENT '提现转账地址',
  `withdraw_private_key` varchar(255)  NOT NULL COMMENT '提现转账地址私钥',
  `withdraw_bnb_balance` decimal(20, 8) NOT NULL DEFAULT 0.00000000 COMMENT '提现地址BNB余额',
  `withdraw_usdt_balance` decimal(20, 8) NOT NULL DEFAULT 0.00000000 COMMENT '提现地址USDT余额',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='链端参数表';
