package com.frontapi.service.impl;

import com.frontapi.service.VerifyService;
import com.frontapi.dto.VerifyImageResponse;
import com.frontapi.dto.VerifyCheckResponse;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import lombok.RequiredArgsConstructor;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.*;
import java.awt.geom.Area;
import java.awt.geom.Rectangle2D;
import java.awt.geom.Ellipse2D;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.UUID;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.awt.geom.AffineTransform;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class VerifyServiceImpl implements VerifyService {

    private final StringRedisTemplate redisTemplate;
    private static final String VERIFY_POSITION_KEY = "verify:position:";
    private static final String VERIFY_TOKEN_KEY = "verify:token:";
    private static final int VERIFY_EXPIRE_MINUTES = 5;
    private static final int TOKEN_EXPIRE_MINUTES = 10;
    private static final String VERIFY_IMAGES_PATH = "/verify/images/";
    private final Random random = new Random();

    @Override
    public VerifyImageResponse generateVerifyImage() {
        try {
            // 1. 读取原始图片
            String[] imageFiles = getVerifyImageFiles();
            String randomImage = imageFiles[random.nextInt(imageFiles.length)];
            BufferedImage originalImage = ImageIO.read(
                getClass().getResourceAsStream(VERIFY_IMAGES_PATH + randomImage)
            );
            
            // 2. 随机生成滑块位置 - 修改位置计算逻辑
            int blockWidth = 50;
            int blockHeight = 50;
            
            // 计算有效的范围
            int maxX = originalImage.getWidth() - blockWidth - 50;  // 右边留出50px边距
            int minX = blockWidth + 50;  // 左边留出50px边距
            int maxY = originalImage.getHeight() - blockHeight - 10; // 底部留出10px边距
            int minY = 10;  // 顶部留出10px边距
            
            // 在有效范围内随机生成位置
            int x = random.nextInt(maxX - minX) + minX;
            int y = random.nextInt(maxY - minY) + minY;
            
            // 3. 生成滑块形状
            Shape blockShape = createBlockShape(blockWidth, blockHeight);
            
            // 4. 生成滑块图片
            BufferedImage blockImage = new BufferedImage(blockWidth, blockHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D blockGraphics = blockImage.createGraphics();
            blockGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            blockGraphics.setClip(blockShape);
            
            // 绘制滑块内容
            blockGraphics.drawImage(originalImage, 0, 0, blockWidth, blockHeight, 
                x, y, x + blockWidth, y + blockHeight, null);
            
            // 添加白色边框
            blockGraphics.setColor(Color.WHITE);
            blockGraphics.setStroke(new BasicStroke(2));
            blockGraphics.draw(blockShape);
            
            // 添加内部阴影效果
            blockGraphics.setColor(new Color(255, 255, 255, 80));
            blockGraphics.fill(blockShape);
            
            blockGraphics.dispose();
            
            // 5. 生成带缺口的背景图
            BufferedImage bgImage = new BufferedImage(originalImage.getWidth(), originalImage.getHeight(), 
                BufferedImage.TYPE_INT_RGB);
            Graphics2D bgGraphics = bgImage.createGraphics();
            bgGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            
            // 绘制背景
            bgGraphics.drawImage(originalImage, 0, 0, null);
            
            // 绘制缺口 - 调整位置
            AffineTransform transform = new AffineTransform();
            transform.translate(x, y);
            Shape translatedShape = transform.createTransformedShape(blockShape);
            
            bgGraphics.setColor(new Color(0, 0, 0, 120));
            bgGraphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 0.8f));
            bgGraphics.fill(translatedShape);
            
            // 绘制缺口边框
            bgGraphics.setColor(new Color(255, 255, 255, 200));
            bgGraphics.setStroke(new BasicStroke(2));
            bgGraphics.draw(translatedShape);
            
            bgGraphics.dispose();
            
            // 6. 保存验证信息
            String key = UUID.randomUUID().toString();
            String redisKey = VERIFY_POSITION_KEY + key;
            
            System.out.println("Saving verify position - Key: " + redisKey + ", Position: " + x);  // 添加日志
            
            redisTemplate.opsForValue().set(
                redisKey, 
                String.valueOf(x), 
                VERIFY_EXPIRE_MINUTES, 
                TimeUnit.MINUTES
            );
            
            // 7. 返回响应
            VerifyImageResponse response = new VerifyImageResponse();
            response.setBgImage("data:image/jpeg;base64," + imageToBase64(bgImage));
            response.setBlockImage("data:image/png;base64," + imageToBase64(blockImage));
            response.setBlockY(y);
            response.setKey(key);
            
            return response;
        } catch (Exception e) {
            throw new RuntimeException("生成验证图片失败", e);
        }
    }

    private Shape createBlockShape(int width, int height) {
        // 创建一个更明显的拼图形状
        int[] blockSize = {width, height};
        int[] circleRadius = {width/4, height/4}; // 增大凸起的大小
        
        // 创建基本矩形
        Rectangle2D.Float rect = new Rectangle2D.Float(0, 0, blockSize[0], blockSize[1]);
        Area area = new Area(rect);
        
        // 添加右边凸起
        Ellipse2D.Float circle = new Ellipse2D.Float(
            blockSize[0] - circleRadius[0]/2,
            blockSize[1]/2 - circleRadius[1],
            circleRadius[0],
            circleRadius[1] * 2
        );
        area.add(new Area(circle));
        
        return area;
    }

    // 获取verify/images目录下的所有图片文件
    private String[] getVerifyImageFiles() throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath:verify/images/*.jpg");
        
        if (resources.length == 0) {
            throw new RuntimeException("验证图片不存在，请确保resources/verify/images/目录下有jpg图片");
        }
        
        return Arrays.stream(resources)
            .map(Resource::getFilename)
            .toArray(String[]::new);
    }

    @Override
    public VerifyCheckResponse checkVerify(Integer moveX, String key) {
        String redisKey = VERIFY_POSITION_KEY + key;
        String correctX = redisTemplate.opsForValue().get(redisKey);
        
        System.out.println("Checking verify position - Key: " + redisKey);
        System.out.println("MoveX: " + moveX + ", CorrectX: " + correctX);
        
        if (correctX == null) {
            System.out.println("Key not found in Redis: " + redisKey);
            // 检查是否存在其他 key
            Set<String> keys = redisTemplate.keys(VERIFY_POSITION_KEY + "*");
            System.out.println("Available keys in Redis: " + keys);
            throw new RuntimeException("验证已过期");
        }
        
        // 2. 检查位置是否正确（增加误差范围）
        int correct = Integer.parseInt(correctX);
        boolean success = Math.abs(moveX - correct) <= 5;
        
        System.out.println("Verify result - Success: " + success + ", Difference: " + Math.abs(moveX - correct));
        
        // 3. 验证完成后删除Redis中的数据
        redisTemplate.delete(redisKey);
        
        VerifyCheckResponse response = new VerifyCheckResponse();
        response.setSuccess(success);
        
        if (success) {
            String token = UUID.randomUUID().toString();
            redisTemplate.opsForValue().set(
                VERIFY_TOKEN_KEY + token,
                "1",
                TOKEN_EXPIRE_MINUTES,
                TimeUnit.MINUTES
            );
            response.setVerifyToken(token);
        }
        
        return response;
    }

    @Override
    public boolean checkToken(String token) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(VERIFY_TOKEN_KEY + token));
    }

    private String imageToBase64(BufferedImage image) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] bytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(bytes);
    }
} 