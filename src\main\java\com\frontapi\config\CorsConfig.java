package com.frontapi.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();

        // 设置允许的具体域名
        List<String> allowedOrigins = new ArrayList<>(Arrays.asList(

            "http://localhost:8090",  // 备用开发环境
            "http://localhost:8099",  // 备用开发环境
                "http://localhost:5174/",
                "http://***********:5173/",
                "https://webapp.catcoinvip.com",
                "https://www.catcoinvip.com",
                "http://**************:5174/"



        ));
        config.setAllowedOrigins(allowedOrigins);

        // 设置允许的域名通配符模式
        List<String> allowedOriginPatterns = Arrays.asList(
            "http://localhost:*", "http://***********:*"
        );
        config.setAllowedOriginPatterns(allowedOriginPatterns);

        // 允许的HTTP方法
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));

        // 允许的请求头
        config.addAllowedHeader("*");

        // 允许携带认证信息
        config.setAllowCredentials(true);

        // 暴露响应头
        config.addExposedHeader("*");

        // 预检请求的有效期，单位为秒
        config.setMaxAge(3600L);

        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
} 