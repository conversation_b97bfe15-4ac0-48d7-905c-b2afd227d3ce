<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="71b13448-464e-4703-9e03-788e14e5d74e" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2pwW6qg6FX2tbhRDBXRsLN5wkDM" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.front-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.front-api [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.front-api [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.FrontApiApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/最新项目文件/交易所/frontapi/src/main/resources/db&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;cccde77313f8e94163768b13aac216cf&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\最新项目文件\交易所\frontapi\src\main\resources\db" />
      <recent name="F:\常规项目\华通云\frontapi\src\main\java\com\frontapi\controller" />
      <recent name="F:\常规项目\华通宝\frontapi\src\main\resources\verify\images" />
      <recent name="G:\备份9\frontapi\src\main\java\com\frontapi\service\impl" />
      <recent name="E:\最新的代码\frontapi\src\main\resources\db" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="FrontApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="front-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.frontapi.FrontApiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="71b13448-464e-4703-9e03-788e14e5d74e" name="更改" comment="" />
      <created>1733677446768</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1733677446768</updated>
      <workItem from="1733677447784" duration="2909000" />
      <workItem from="1733708048998" duration="15550000" />
      <workItem from="1733735279080" duration="23592000" />
      <workItem from="1733804132172" duration="8478000" />
      <workItem from="1733822221894" duration="9000" />
      <workItem from="1733822240852" duration="7000" />
      <workItem from="1733851431635" duration="3907000" />
      <workItem from="1733901050370" duration="16524000" />
      <workItem from="1733968223704" duration="7756000" />
      <workItem from="1733986345704" duration="2068000" />
      <workItem from="1733993960813" duration="2873000" />
      <workItem from="1734026304396" duration="681000" />
      <workItem from="1734062811832" duration="9814000" />
      <workItem from="1734083147618" duration="1340000" />
      <workItem from="1734088951516" duration="5235000" />
      <workItem from="1734115799570" duration="456000" />
      <workItem from="1734117198996" duration="892000" />
      <workItem from="1734139556538" duration="5868000" />
      <workItem from="1734233511719" duration="573000" />
      <workItem from="1734318197955" duration="14000" />
      <workItem from="1734321334483" duration="902000" />
      <workItem from="1734322475607" duration="47000" />
      <workItem from="1734483809652" duration="516000" />
      <workItem from="1734493732639" duration="7288000" />
      <workItem from="1734584829078" duration="2240000" />
      <workItem from="1734657102604" duration="3188000" />
      <workItem from="1734686873361" duration="130000" />
      <workItem from="1734746090229" duration="2634000" />
      <workItem from="1734914448574" duration="5020000" />
      <workItem from="1734928453872" duration="3502000" />
      <workItem from="1734932641209" duration="74000" />
      <workItem from="1734934921739" duration="769000" />
      <workItem from="1734940047066" duration="699000" />
      <workItem from="1734952377903" duration="3315000" />
      <workItem from="1734965353450" duration="3013000" />
      <workItem from="1735019465433" duration="75000" />
      <workItem from="1735043088400" duration="599000" />
      <workItem from="1735094543524" duration="12280000" />
      <workItem from="1735265858055" duration="1295000" />
      <workItem from="1735268963210" duration="153000" />
      <workItem from="1735270764583" duration="1837000" />
      <workItem from="1735273868580" duration="1192000" />
      <workItem from="1735626020285" duration="2306000" />
      <workItem from="1735637324235" duration="3028000" />
      <workItem from="1736053340274" duration="6000" />
      <workItem from="1736055788408" duration="3021000" />
      <workItem from="1736082755573" duration="14000" />
      <workItem from="1736143363812" duration="28000" />
      <workItem from="1736144094158" duration="203000" />
      <workItem from="1736147803342" duration="737000" />
      <workItem from="1736262935494" duration="663000" />
      <workItem from="1736415374659" duration="222000" />
      <workItem from="1736743160233" duration="9176000" />
      <workItem from="1737346512592" duration="544000" />
      <workItem from="1737358850120" duration="1090000" />
      <workItem from="1737614548926" duration="2148000" />
      <workItem from="1737617854207" duration="221000" />
      <workItem from="1738819348940" duration="4691000" />
      <workItem from="1738848347322" duration="1647000" />
      <workItem from="1738910025394" duration="31000" />
      <workItem from="1742284560557" duration="3171000" />
      <workItem from="1742958773086" duration="10841000" />
      <workItem from="1743138887344" duration="1626000" />
      <workItem from="1743159000732" duration="1816000" />
      <workItem from="1743168793696" duration="4000" />
      <workItem from="1743171558393" duration="1038000" />
      <workItem from="1743381148029" duration="384000" />
      <workItem from="1743383310863" duration="2030000" />
      <workItem from="1743420370738" duration="479000" />
      <workItem from="1743466882315" duration="507000" />
      <workItem from="1743467450330" duration="43000" />
      <workItem from="1743484446223" duration="436000" />
      <workItem from="1743488590397" duration="1537000" />
      <workItem from="1743567160754" duration="59000" />
      <workItem from="1743570070384" duration="411000" />
      <workItem from="1743571908102" duration="1531000" />
      <workItem from="1743574107414" duration="4000" />
      <workItem from="1744619654355" duration="1508000" />
      <workItem from="1744769624292" duration="2283000" />
      <workItem from="1744875932130" duration="6285000" />
      <workItem from="1745218705988" duration="538000" />
      <workItem from="1745371706205" duration="22000" />
      <workItem from="1745371767374" duration="4476000" />
      <workItem from="1745843500890" duration="227000" />
      <workItem from="1747114342653" duration="741000" />
      <workItem from="1747271671526" duration="3230000" />
      <workItem from="1747383820936" duration="468000" />
      <workItem from="1749046538377" duration="2599000" />
      <workItem from="1749356869936" duration="70000" />
      <workItem from="1749397166930" duration="13000" />
      <workItem from="1751036587213" duration="2036000" />
      <workItem from="1751121480403" duration="2137000" />
      <workItem from="1751149144444" duration="92000" />
      <workItem from="1751149986830" duration="78000" />
      <workItem from="1751150310221" duration="553000" />
      <workItem from="1751185269798" duration="7396000" />
      <workItem from="1751201401666" duration="2885000" />
      <workItem from="1751428132396" duration="14616000" />
      <workItem from="1751615451799" duration="3900000" />
      <workItem from="1751696226816" duration="3293000" />
      <workItem from="1751789627411" duration="6452000" />
      <workItem from="1751948646162" duration="16422000" />
      <workItem from="1752022632319" duration="11366000" />
      <workItem from="1752072076063" duration="4757000" />
      <workItem from="1752115141408" duration="20505000" />
      <workItem from="1752215822948" duration="18676000" />
      <workItem from="1752319505431" duration="1945000" />
      <workItem from="1752375882837" duration="3199000" />
      <workItem from="1752407349646" duration="1274000" />
      <workItem from="1752415800014" duration="2022000" />
      <workItem from="1752921011958" duration="185000" />
      <workItem from="1752921586147" duration="1023000" />
      <workItem from="1752995535216" duration="7877000" />
      <workItem from="1753073786294" duration="8000" />
      <workItem from="1753074304637" duration="16722000" />
      <workItem from="1753246683797" duration="24586000" />
      <workItem from="1753364241576" duration="902000" />
      <workItem from="1753414144370" duration="3797000" />
      <workItem from="1753434942455" duration="732000" />
      <workItem from="1753440282481" duration="12951000" />
      <workItem from="1753539898667" duration="7000000" />
      <workItem from="1753579040267" duration="1298000" />
      <workItem from="1753585434296" duration="5746000" />
      <workItem from="1753606039965" duration="2866000" />
      <workItem from="1753609804575" duration="140000" />
      <workItem from="1753610166690" duration="4695000" />
      <workItem from="1753623410076" duration="881000" />
      <workItem from="1753625813468" duration="1166000" />
      <workItem from="1753663182748" duration="668000" />
      <workItem from="1753666437239" duration="1133000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava2:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/UserBankCardServiceImpl.java</url>
          <line>43</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/controller/UserBankCardController.java</url>
          <line>63</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/WithdrawServiceImpl.java</url>
          <line>110</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/spring-web/5.3.20/spring-web-5.3.20-sources.jar!/org/springframework/web/method/support/InvocableHandlerMethod.java</url>
          <line>211</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/UserServiceImpl.java</url>
          <line>563</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/controller/AuthController.java</url>
          <line>23</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/controller/CopyOrderController.java</url>
          <line>185</line>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/SmsServiceImpl.java</url>
          <line>129</line>
          <properties class="com.frontapi.service.impl.SmsServiceImpl" method="sendSmsMessage">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/controller/AuthController.java</url>
          <line>27</line>
          <properties class="com.frontapi.controller.AuthController" method="checkPhone">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/UserServiceImpl.java</url>
          <line>221</line>
          <properties class="com.frontapi.service.impl.UserServiceImpl" method="getCurrentUserInfo">
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/frontapi/service/impl/UserServiceImpl.java</url>
          <line>529</line>
          <properties class="com.frontapi.service.impl.UserServiceImpl" method="resetPassword">
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>