package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("sys_banner")
public class SysBanner {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;
    
    @TableField("image_url")
    private String imageUrl;
    
    @TableField("jump_url")
    private String jumpUrl;
    
    private Integer sort;
    
    private Integer status;
    
    @TableField("create_by")
    private String createBy;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField("update_by")
    private String updateBy;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 