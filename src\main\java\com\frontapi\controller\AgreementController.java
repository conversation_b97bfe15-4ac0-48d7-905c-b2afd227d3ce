package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.Agreement;
import com.frontapi.service.AgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/agreement")
public class AgreementController {

    @Autowired
    private AgreementService agreementService;

    @GetMapping("/list")
    public ApiResponse<List<Agreement>> list() {
        LambdaQueryWrapper<Agreement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agreement::getStatus, 1)  // 只查询启用的条款
               .orderByAsc(Agreement::getId);
        
        List<Agreement> list = agreementService.list(wrapper);
        return ApiResponse.success(list);
    }

    @GetMapping("/detail/{id}")
    public ApiResponse<Agreement> detail(@PathVariable Long id) {
        Agreement agreement = agreementService.getAgreementDetail(id);
        if (agreement == null) {
            return ApiResponse.error("条款不存在");
        }
        return ApiResponse.success(agreement);
    }
} 