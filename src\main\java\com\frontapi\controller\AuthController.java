package com.frontapi.controller;

import com.frontapi.dto.*;
// import com.frontapi.entity.FrontUser;
import com.frontapi.service.SmsService;
import com.frontapi.service.UserService;
// import com.frontapi.service.VerifyService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import lombok.Data;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;
    private final SmsService smsService;
 
    private final StringRedisTemplate redisTemplate;
    @PostMapping("/register")
    public ApiResponse<Boolean> register(@RequestBody RegisterRequest request) {
        return ApiResponse.success(userService.register(request));
    }

    @GetMapping("/check-phone")
    public ApiResponse<Boolean> checkPhone(@RequestParam String phone) {
        return ApiResponse.success(userService.existsByPhone(phone));
    }

    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
        LoginResponse response = userService.login(request.getPhone(), request.getEmail(), request.getPassword());
        return ApiResponse.success(response);
    }

    @PostMapping("/login/code")
    public ApiResponse<LoginResponse> loginByCode(@RequestBody LoginByCodeRequest request) {
        LoginResponse response = userService.loginByCode(request.getPhone(), request.getCode());
        return ApiResponse.success(response);
    }

    @PostMapping("/send-code-reg-okReg-2025")
    public ApiResponse<Void> sendCodeReg(
            @RequestParam String phone,
            @RequestParam String captchaKey,
            @RequestParam String captchaCode
    ) {
        try {
            String cacheKey = "captcha:" + captchaKey;
            String realCode = redisTemplate.opsForValue().get(cacheKey);
            if (realCode == null || !realCode.equalsIgnoreCase(captchaCode)) {
                return ApiResponse.error("图片验证码错误");
            }
            redisTemplate.delete(cacheKey);

            smsService.sendCode(phone);
            return ApiResponse.success(null);
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    @PostMapping("/send-code")
    public ApiResponse<Void> sendCode(@RequestParam String phone) {
        smsService.sendCode(phone);
        return ApiResponse.success(null);
    }

    @PostMapping("/send-code-login")
    public ApiResponse<Void> sendCodeLogin(@RequestParam String phone) {
        try {
            smsService.sendLoginCode(phone);
            return ApiResponse.success(null);
        } catch (RuntimeException e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    @PostMapping("/send-reset-code")
    public ApiResponse<Void> sendResetCode(@RequestBody ResetCodeRequest request) {
        userService.sendResetCode(request.getPhone(), request.getEmail());
        return ApiResponse.success(null);
    }

    @PostMapping("/reset-password")
    public ApiResponse<Void> resetPassword(@RequestBody ResetPasswordRequest request) {
        userService.resetPassword(request.getPhone(), request.getEmail(), request.getCode(), request.getNewPassword());
        return ApiResponse.success(null);
    }

    /**
     * 发送重置密码验证码（手机）
     * 实际验证码会输出到后台控制台日志，不会真正发送到手机
     * 前端调用时传递 phone 参数
     */
    @PostMapping("/send-reset-code-phone")
    public ApiResponse<Void> sendResetCodePhone(@RequestParam String phone) {
        userService.sendResetCode(phone, null);
        return ApiResponse.success(null);
    }

    /**
     * 发送重置密码验证码（邮箱）
     * 实际验证码会输出到后台控制台日志，不会真正发送到邮箱
     * 前端调用时传递 email 参数
     */
    @PostMapping("/send-reset-code-email")
    public ApiResponse<Void> sendResetCodeEmail(@RequestParam String email) {
        userService.sendResetCode(null, email);
        return ApiResponse.success(null);
    }

    /**
     * 注册时发送邮箱验证码
     */
    @PostMapping("/send-reg-code-email")
    public ApiResponse<Void> sendRegisterEmailCode(@RequestParam String email) {
        userService.sendRegisterEmailCode(email);
        return ApiResponse.success(null);
    }

    @Data
    static class ResetCodeRequest {
        private String phone;
        private String email;
    }

    @Data
    static class ResetPasswordRequest {
        private String phone;
        private String email;
        private String code;
        private String newPassword;
    }
} 