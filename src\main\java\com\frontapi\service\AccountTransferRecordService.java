package com.frontapi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frontapi.entity.AccountTransferRecord;

public interface AccountTransferRecordService extends IService<AccountTransferRecord> {
    
    /**
     * 分页查询用户的划转记录
     */
    Page<AccountTransferRecord> getTransferRecords(Long userId, int page, int size);
    
    /**
     * 创建划转记录
     */
    boolean createTransferRecord(AccountTransferRecord record);
    
    /**
     * 更新划转记录状态
     */
    boolean updateTransferStatus(Long recordId, Integer status, String remark);
} 