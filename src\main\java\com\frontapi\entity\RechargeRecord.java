package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("recharge_record")
public class RechargeRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    private String username;
    
    private String phone;
    
    private BigDecimal amount;
    
    @TableField("recharge_type")
    private Integer rechargeType;
    
    @TableField("audit_status")
    private Integer auditStatus;
    
    @TableField("proof_image")
    private String proofImage;
    
    private String remark;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 