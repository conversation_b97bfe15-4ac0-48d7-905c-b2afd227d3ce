package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frontapi.entity.SysParams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface SysParamsMapper extends BaseMapper<SysParams> {

    @Select("SELECT copy_trade_fee FROM sys_params LIMIT 1")
    BigDecimal getCopyTradeFee();

    @Select("SELECT platform_fee_rate FROM sys_params LIMIT 1")
    BigDecimal getPlatformFeeRate();
}