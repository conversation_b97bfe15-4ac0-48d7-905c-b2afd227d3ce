package com.frontapi.mapper;

import com.frontapi.entity.FuturesOptionOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface FuturesOptionOrderMapper extends BaseMapper<FuturesOptionOrder> {
    // 分页查询持仓订单（status=0）
    List<FuturesOptionOrder> selectHoldOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countHoldOrders(@Param("userId") Long userId);

    // 分页查询盈利订单（status=1 且 profit>0）
    List<FuturesOptionOrder> selectProfitOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countProfitOrders(@Param("userId") Long userId);

    // 分页查询成交明细（status=1）
    List<FuturesOptionOrder> selectDealOrders(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    int countDealOrders(@Param("userId") Long userId);

    // 全平台盈利订单分页
    List<FuturesOptionOrder> selectAllProfitOrders(@Param("offset") int offset, @Param("limit") int limit);
    int countAllProfitOrders();

    IPage<FuturesOptionOrder> selectHoldOrdersPage(Page<FuturesOptionOrder> page, Long userId);
    IPage<FuturesOptionOrder> selectDealOrdersPage(Page<FuturesOptionOrder> page, Long userId);
    IPage<FuturesOptionOrder> selectAllProfitOrdersPage(Page<FuturesOptionOrder> page);
    int insert(FuturesOptionOrder order);

    /**
     * 查询用户所有持仓订单（status=0）
     * 用于实时盈利推送
     */
    List<FuturesOptionOrder> selectHoldOrdersByUserId(@Param("userId") Long userId);

    /**
     * 查询用户所有持仓订单（status in 0,1,2）
     * 用于实时盈利推送（含已成交/已结算但未超5分钟）
     */
    List<FuturesOptionOrder> selectAllHoldOrdersByUserId(@Param("userId") Long userId);

    /**
     * 统计今日买涨/买跌次数
     */
    int countTodayDirection(@Param("userId") Long userId, @Param("direction") Integer direction, @Param("startTime") java.util.Date startTime, @Param("endTime") java.util.Date endTime);
} 