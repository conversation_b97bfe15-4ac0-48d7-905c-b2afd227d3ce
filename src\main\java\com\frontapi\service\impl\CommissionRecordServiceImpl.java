package com.frontapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.CommissionRecord;
import com.frontapi.mapper.CommissionRecordMapper;
import com.frontapi.service.CommissionRecordService;
import com.frontapi.vo.CommissionStatsVO;
import com.frontapi.vo.ProfitSummaryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CommissionRecordServiceImpl extends ServiceImpl<CommissionRecordMapper, CommissionRecord> implements CommissionRecordService {

    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    @Override
    public CommissionStatsVO getCommissionStats(Long userId) {
        return commissionRecordMapper.getCommissionStats(userId);
    }

    @Override
    public ProfitSummaryVO getProfitSummary(Long userId) {
        return commissionRecordMapper.getProfitSummary(userId);
    }
}