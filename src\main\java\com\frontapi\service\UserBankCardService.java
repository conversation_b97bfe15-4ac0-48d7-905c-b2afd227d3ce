package com.frontapi.service;

import com.frontapi.entity.UserBankCard;
import java.util.List;

public interface UserBankCardService {
    // 获取用户银行卡列表
    List<UserBankCard> getUserBankCards();
    
    // 检查银行卡是否存在
    boolean existsByUserIdAndCardNo(Long userId, String cardNumber);
    
    // 添加银行卡
    void addBankCard(Long userId, UserBankCard bankCard);
    
    // 解绑银行卡
    boolean unbindBankCard(Long cardId);
    
    // 根据用户 ID 获取用户自己的银行卡
    List<UserBankCard> getByUserId(Long userId);
} 