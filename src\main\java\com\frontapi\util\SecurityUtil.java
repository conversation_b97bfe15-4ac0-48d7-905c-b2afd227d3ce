package com.frontapi.util;

import com.frontapi.entity.FrontUser;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class SecurityUtil {
    
    /**
     * 获取当前登录用户ID
     */
    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof FrontUser) {
            FrontUser user = (FrontUser) authentication.getPrincipal();
            return user.getId();
        }
        return null;
    }

    /**
     * 获取当前登录用户信息
     */
    public FrontUser getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof FrontUser) {
            return (FrontUser) authentication.getPrincipal();
        }
        return null;
    }

    /**
     * 静态方法，方便直接调用
     */
    public static FrontUser getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof FrontUser) {
            return (FrontUser) authentication.getPrincipal();
        }
        return null;
    }

    /**
     * 静态方法，方便直接调用
     */
    public static Long getUserId() {
        FrontUser user = getUser();
        return user != null ? user.getId() : null;
    }
} 