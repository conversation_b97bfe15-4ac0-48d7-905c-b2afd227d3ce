package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.TradeRecord;
import com.frontapi.exception.BusinessException;
import com.frontapi.mapper.TradeRecordMapper;
import com.frontapi.service.ITradeRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易明细表 服务实现类
 */
@Service
@RequiredArgsConstructor
public class TradeRecordServiceImpl extends ServiceImpl<TradeRecordMapper, TradeRecord> implements ITradeRecordService {
    private final UserService userService;
    @Override
    public IPage<TradeRecord> getTradeRecordPage(Integer page, Integer size, Long userId, Integer accountType) {
        Page<TradeRecord> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<TradeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(userId != null, TradeRecord::getUserId, userId);
        queryWrapper.eq(accountType != null, TradeRecord::getAccountType, accountType);
        queryWrapper.orderByDesc(TradeRecord::getCreateTime);
        
        return this.page(pageParam, queryWrapper);
    }

    @Override
    public void addTradeRecord(Long userId, String username, String tradeType, 
                              BigDecimal amount, Integer accountType, String remark) {
        TradeRecord tradeRecord = new TradeRecord();
        tradeRecord.setUserId(userId);
        tradeRecord.setUsername(username);
        tradeRecord.setTradeType(tradeType);
        tradeRecord.setAmount(amount);
        tradeRecord.setAccountType(accountType);
        tradeRecord.setRemark(remark);
        tradeRecord.setCreateTime(LocalDateTime.now());
        tradeRecord.setUpdateTime(LocalDateTime.now());
        
        this.save(tradeRecord);
    }
} 