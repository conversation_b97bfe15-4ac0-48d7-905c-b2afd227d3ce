package com.frontapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.RechargeRecord;
import com.frontapi.service.RechargeRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/recharge")
@RequiredArgsConstructor
public class RechargeRecordController {

    private final RechargeRecordService rechargeRecordService;
    private final UserService userService;

    @GetMapping("/list")
    public ApiResponse<Page<RechargeRecord>> getRechargeList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }

        Page<RechargeRecord> records = rechargeRecordService.getRechargeList(
            currentUser.getId(), page, pageSize);
            
        return ApiResponse.success(records);
    }
} 