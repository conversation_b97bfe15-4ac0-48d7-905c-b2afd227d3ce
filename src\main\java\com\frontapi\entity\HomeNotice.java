package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("home_notice")
public class HomeNotice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;
    
    private String content;
    
    private Integer status;
    
    @TableField("create_time")
    private LocalDateTime createTime;
} 