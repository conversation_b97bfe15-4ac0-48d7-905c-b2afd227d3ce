package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("sys_params")
public class SysParams {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("min_transfer")
    private BigDecimal minTransfer;  // 最低转账限额

    @TableField("max_transfer")
    private BigDecimal maxTransfer;  // 最高转账限额

    @TableField("transfer_fee")
    private BigDecimal transferFee;  // 转账手续费

    @TableField("enable_transfer")
    private Boolean enableTransfer;  // 是否允许转账(0:禁用,1:启用)

    @TableField("min_withdraw")
    private BigDecimal minWithdraw;  // 最低提现限额

    @TableField("max_withdraw")
    private BigDecimal maxWithdraw;  // 最高提现限额

    @TableField("max_auto_withdraw")
    private BigDecimal maxAutoWithdraw; // 提现最高自动转账

    @TableField("withdraw_fee")
    private BigDecimal withdrawFee;  // 提现手续费

    @TableField("enable_withdraw")
    private Boolean enableWithdraw;  // 是否允许提现(0:禁用,1:启用)

    @TableField("auto_withdraw")
    private Boolean autoWithdraw;    // 是否自动提现 0-关闭 1-开启

    @TableField("enable_internal_transfer")
    private Boolean enableInternalTransfer;  // 是否允许内部转账 0-关闭 1-开启

    @TableField("min_copy_trade")
    private BigDecimal minCopyTrade; // 最低跟单额度

    @TableField("trade_profit_rate")
    private BigDecimal tradeProfitRate; // 交易盈利比例

    @TableField("copy_trade_account_rate")
    private BigDecimal copyTradeAccountRate; // 每次跟单的跟单账户比例

    @TableField("copy_trade_fee")
    private BigDecimal copyTradeFee; // 每笔跟单手续费比例

    @TableField("platform_fee_rate")
    private BigDecimal platformFeeRate; // 平台预留手续费比例

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;         // 创建时间

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;         // 更新时间
} 