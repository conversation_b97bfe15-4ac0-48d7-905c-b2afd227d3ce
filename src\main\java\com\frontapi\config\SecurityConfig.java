package com.frontapi.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .cors().and()
            .csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
            .antMatchers(
                "/static/**",                // 静态资源目录
                "/kline-sse-demo.html", 
                "/upload/**",                // 允许直接访问图片资源
                "/api/auth/login",           // 登录
                "/api/auth/register",        // 注册
                "/api/auth/send-code",     // 发送验证码
                "/api/auth/send-code-login",
                "/api/auth/send-code-reg-okReg-2025",
                "/api/agreement/detail/**",
                "/api/auth/login/code",
                "/api/auth/check-phone",
                    "/api/auth/verify/get",
                    "/api/auth/verify/check",
                    "/api/auth/captcha",
                    "/api/auth/send-reset-code-phone",
                    "/api/auth/send-reset-code-email",
                    "/api/auth/reset-password",              
                    "/api/market/kline/stream",
                    "/api/market/kline/stream/symbol",
                    "/api/market/kline/ticker",
                    "/api/market/kline/kline",
                    "/api/market/kline/depth",
                    "/api/market/kline/all", 
                     "/api/market/kline/**",                   
                     "/api/auth/send-reg-code-email"
                       
            ).permitAll()
            .anyRequest().authenticated()
            .and()
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
        StrictHttpFirewall firewall = new StrictHttpFirewall();
        firewall.setAllowUrlEncodedSlash(true);
        firewall.setAllowSemicolon(true);
        firewall.setAllowBackSlash(true);
        firewall.setAllowUrlEncodedDoubleSlash(true);  // 允许双斜杠
        return firewall;
    }
} 