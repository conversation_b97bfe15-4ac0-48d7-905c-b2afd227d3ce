package com.frontapi.controller;

import com.frontapi.service.WebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.Map;

@Slf4j
@Controller
public class WebSocketController {
    
    @Autowired
    private WebSocketService webSocketService;
    
    /**
     * 订阅交易对
     */
    @MessageMapping("/subscribe")
    @SendTo("/topic/subscription")
    public Map<String, Object> subscribe(Map<String, String> message) {
        String symbol = message.get("symbol");
        if (symbol != null && !symbol.isEmpty()) {
            webSocketService.subscribeSymbol(symbol);
            Map<String, Object> response = new java.util.HashMap<>();
            response.put("type", "subscribe");
            response.put("symbol", symbol);
            response.put("status", "success");
            response.put("message", "订阅成功");
            return response;
        }
        Map<String, Object> response = new java.util.HashMap<>();
        response.put("type", "subscribe");
        response.put("status", "error");
        response.put("message", "交易对不能为空");
        return response;
    }
    
    /**
     * 取消订阅
     */
    @MessageMapping("/unsubscribe")
    @SendTo("/topic/subscription")
    public Map<String, Object> unsubscribe(Map<String, String> message) {
        String symbol = message.get("symbol");
        if (symbol != null && !symbol.isEmpty()) {
            webSocketService.unsubscribeSymbol(symbol);
            Map<String, Object> response = new java.util.HashMap<>();
            response.put("type", "unsubscribe");
            response.put("symbol", symbol);
            response.put("status", "success");
            response.put("message", "取消订阅成功");
            return response;
        }
        Map<String, Object> response = new java.util.HashMap<>();
        response.put("type", "unsubscribe");
        response.put("status", "error");
        response.put("message", "交易对不能为空");
        return response;
    }
} 