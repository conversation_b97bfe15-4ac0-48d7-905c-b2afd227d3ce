package com.frontapi.controller;

import com.frontapi.common.Result;
import com.frontapi.entity.TransferWithdrawParamsVO;
import com.frontapi.service.SysParamsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/sys/params")
@RequiredArgsConstructor
public class SysParamsController {

    private final SysParamsService sysParamsService;

    @GetMapping
    public Result<?> getSysParams() {
        return Result.ok(sysParamsService.getSysParams());
    }

    @GetMapping("/transfer-withdraw")
    public Result<TransferWithdrawParamsVO> getTransferWithdrawParams() {
        return Result.ok(sysParamsService.getTransferWithdrawParams());
    }
} 