package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易明细表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("trade_record")
public class TradeRecord {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易金额（可正可负）
     */
    private BigDecimal amount;

    /**
     * 归属账户(1:资金账户,2:跟单账户)
     */
    private Integer accountType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 