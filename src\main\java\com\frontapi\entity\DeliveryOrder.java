package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("delivery_order")
public class DeliveryOrder {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Long userId;
    private Long leaderId;
    private String symbol;
    private BigDecimal marginAmount;
    private BigDecimal positionAmount;
    private Integer lever;
    private Integer direction;
    private BigDecimal takeProfit;
    private BigDecimal stopLoss;
    private BigDecimal openPrice;
    private BigDecimal closePrice;
    private Date openTime;
    private Date closeTime;
    private Integer status;
    private BigDecimal profit;
    private Integer rebateStatus;
    private Integer profitStatus;
    private Integer isSettlement;
    private Date createTime;
    private Date updateTime;
} 