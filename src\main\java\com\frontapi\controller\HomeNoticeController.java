package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.HomeNotice;
import com.frontapi.mapper.HomeNoticeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/notice")
@RequiredArgsConstructor
public class HomeNoticeController {
    
    private final HomeNoticeMapper homeNoticeMapper;
    
    @GetMapping("/active")
    public ApiResponse<HomeNotice> getActiveNotice() {
        try {
            HomeNotice notice = homeNoticeMapper.selectActiveNotice();
            return ApiResponse.success(notice);
        } catch (Exception e) {
            log.error("获取公告失败", e);
            return ApiResponse.error("获取公告失败");
        }
    }
} 