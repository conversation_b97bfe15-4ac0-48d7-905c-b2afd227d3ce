package com.frontapi.controller;

import com.frontapi.common.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.frontapi.config.UploadPathConfig;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.util.UUID;
import java.io.IOException;
import java.nio.file.Files;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/api/file")
@RequiredArgsConstructor
public class FileController {

    @Autowired
    private UploadPathConfig uploadPathConfig;

    @Value("${upload.allowed-types}")
    private String allowedTypes;

    @PostMapping("/upload")
    public Result<?> upload(@RequestParam("file") MultipartFile file, 
                           @RequestParam(value = "type", required = false) String type) {
        try {
            if (file.isEmpty()) {
                return Result.fail("请选择文件");
            }

            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            String fileType = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
            if (!allowedTypes.contains(fileType)) {
                return Result.fail("不支持的文件类型");
            }

            // 根据type参数确定子目录
            String subDir = "helping";  // 默认目录
            if ("banner".equals(type)) {
                subDir = "banner";
            }
            
            // 生成新的文件名
            String newFileName = UUID.randomUUID().toString() + "." + fileType;
            
            // 确保上传目录存在（包含子目录）
            String fullUploadPath = uploadPathConfig.getUploadPath();
            if (!fullUploadPath.endsWith("/")) {
                fullUploadPath += "/";
            }
            fullUploadPath += subDir;
            
            File uploadDir = new File(fullUploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // 保存文件到对应子目录
            File destFile = new File(fullUploadPath + "/" + newFileName);
            file.transferTo(destFile);
            
            // 返回相对路径（添加前导斜杠）
            return Result.ok("/upload/" + subDir + "/" + newFileName);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("文件上传失败：" + e.getMessage());
        }
    }

    // 文件访问接口
    @GetMapping("/images/**")
    public void getImage(HttpServletRequest request, HttpServletResponse response) {
        try {
            String requestURI = request.getRequestURI();
            String imagePath = requestURI.substring("/api/file/images".length());
            
            // 规范化路径，移除所有多余的斜杠
            imagePath = imagePath.replaceAll("^/+", "")  // 移除开头的斜杠
                               .replaceAll("/+", "/");    // 将多个斜杠替换为单个斜杠
            
            // 构建完整的文件路径
            String fullPath = uploadPathConfig.getUploadPath();
            if (!fullPath.endsWith("/")) {
                fullPath += "/";
            }
            fullPath += imagePath;
            
            File file = new File(fullPath);
            
            if (!file.exists()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应类型
            String fileType = imagePath.substring(imagePath.lastIndexOf(".") + 1);
            response.setContentType("image/" + fileType);
            
            // 将文件写入响应
            Files.copy(file.toPath(), response.getOutputStream());
            response.getOutputStream().flush();
        } catch (IOException e) {
            e.printStackTrace();
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
} 