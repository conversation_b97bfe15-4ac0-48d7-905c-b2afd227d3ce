package com.frontapi.controller;

import com.frontapi.common.Result;
import com.frontapi.dto.AddBankCardDTO;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.UserBankCard;
import com.frontapi.exception.BusinessException;
import com.frontapi.service.UserBankCardService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


import java.util.List;

@RestController
@RequestMapping("/api/user/bank-cards")

@RequiredArgsConstructor
public class UserBankCardController {

    private final UserBankCardService userBankCardService;
    private final UserService userService;
    @GetMapping("/list")
    public Result<List<UserBankCard>> getBankCards() {
        // 获取当前登录用户信息
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        try {
            // 获取用户的银行卡列表
            List<UserBankCard> bankCards = userBankCardService.getByUserId(currentUser.getId());
            return Result.ok(bankCards);
        } catch (Exception e) {
            return Result.fail("获取银行卡列表失败：" + e.getMessage());
        }
    }

    @PostMapping("/add")
    public Result<?> addBankCard(@RequestBody AddBankCardDTO dto) {
        // 获取当前登录用户
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        try {
            // 将DTO转换为实体
            UserBankCard bankCard = new UserBankCard();
            bankCard.setBankName(dto.getBankName());
            bankCard.setCardNumber(dto.getCardNo());
            bankCard.setCardHolder(dto.getAccountName());
            bankCard.setPhone(dto.getPhone());
            bankCard.setIdNumber(dto.getIdCard());
            bankCard.setBankBranch(dto.getBranchName());
            bankCard.setIsDefault(false);
            bankCard.setStatus(1);
            
            // 使用当前登录用户的ID
            userBankCardService.addBankCard(currentUser.getId(), bankCard);
            return Result.ok();
        } catch (BusinessException e) {
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            return Result.fail("添加银行卡失败");
        }
    }

    @PostMapping("/unbind/{cardId}")
    public Result<?> unbindBankCard(@PathVariable Long cardId) {
        boolean success = userBankCardService.unbindBankCard(cardId);
        return success ? Result.ok() : Result.fail("解绑失败");
    }
} 