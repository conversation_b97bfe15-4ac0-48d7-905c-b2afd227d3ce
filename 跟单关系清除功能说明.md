# 跟单关系清除功能说明

## 功能概述

在一键平仓和自动平仓时，系统会自动检查并清除不满足跟单条件的用户关系。这确保了只有符合带单员配置要求的用户才能继续跟单。

## 核心功能

### 1. 自动触发时机
- **一键平仓后**：带单员手动平仓订单后自动执行
- **自动平仓后**：系统自动平仓（止盈/止损）后自动执行

### 2. 检查条件
系统会验证每个跟单用户的账户余额是否满足以下条件：

#### 最低跟单金额验证
- 用户的 `copy_trade_balance` 必须 >= `copy_config.min_follow_amount`
- 如果余额不足，自动清除跟单关系

#### 最高跟单金额验证
- 如果 `copy_config.max_follow_amount` > 0，则用户余额必须 <= 最高金额
- 如果 `max_follow_amount` = 0，表示无限制，不进行最高金额验证
- 如果余额超限，自动清除跟单关系

### 3. 清除操作
当用户不满足条件时，系统会：
- 将用户的 `is_following` 字段设置为 0
- 将用户的 `leader_id` 字段设置为 0
- 将用户的 `copy_trade_frozen_status` 字段设置为 0（解锁跟单账户）
- 记录详细的操作日志

## 技术实现

### 核心服务类

#### FollowRelationCleanupService
```java
public interface FollowRelationCleanupService {
    // 清除指定带单员的所有无效跟单关系
    int cleanupInvalidFollowRelations(Long leaderId);
    
    // 检查并清除单个用户的跟单关系
    boolean checkAndCleanupSingleFollowRelation(Long followerId, Long leaderId);
}
```

#### FollowRelationCleanupServiceImpl
- 实现具体的余额验证逻辑
- 执行数据库更新操作
- 提供详细的日志记录

### 集成点

#### 1. 自动平仓服务 (AutoCloseServiceImpl)
```java
// 在自动平仓成功后调用
if (currentOrder.getUserId().equals(currentOrder.getLeaderId())) {
    processFollowerOrders(currentOrder, reason);
    
    // 清除不满足条件的跟单关系
    int cleanupCount = followRelationCleanupService.cleanupInvalidFollowRelations(currentOrder.getLeaderId());
}
```

#### 2. 一键平仓服务 (DeliveryOrderServiceImpl)
```java
// 在一键平仓成功后调用
if (order.getUserId().equals(order.getLeaderId())) {
    processFollowerOrdersForManualClose(order);
    
    // 清除不满足条件的跟单关系
    int cleanupCount = followRelationCleanupService.cleanupInvalidFollowRelations(order.getLeaderId());
}
```

## 数据库表结构

### front_user 表相关字段
```sql
`copy_trade_balance` decimal(30,4) DEFAULT '0.0000' COMMENT '跟单账户余额',
`is_following` tinyint(4) DEFAULT '0' COMMENT '是否一键跟单(0:否,1:是)',
`leader_id` bigint(20) DEFAULT '0' COMMENT '带单员ID',
`copy_config_id` bigint(20) DEFAULT NULL COMMENT 'copy_config表的id(只有带单人才有)',
`copy_trade_frozen_status` tinyint(4) DEFAULT '0' COMMENT '跟单账户锁定状态(0:未锁定,1:已锁定)'
```

### copy_config 表相关字段
```sql
`min_follow_amount` decimal(20,8) DEFAULT '0.00000000' COMMENT '最低跟单金额（0为不限制）',
`max_follow_amount` decimal(20,8) DEFAULT '0.00000000' COMMENT '最高跟单金额（0为不限制）'
```

## 使用示例

### 配置示例
```sql
-- 短线配置：最低1000，最高2000
INSERT INTO copy_config (name, min_follow_amount, max_follow_amount) 
VALUES ('短线-随进随出', 1000.00, 2000.00);

-- 中线配置：最低2000，无上限
INSERT INTO copy_config (name, min_follow_amount, max_follow_amount) 
VALUES ('中线-锁仓30天', 2000.00, 0.00);
```

### 清除场景示例

#### 场景1：余额不足
- 用户余额：500 USDT
- 最低要求：1000 USDT
- 结果：清除跟单关系

#### 场景2：余额超限
- 用户余额：3000 USDT
- 最高限制：2000 USDT
- 结果：清除跟单关系

#### 场景3：余额正常
- 用户余额：1500 USDT
- 范围要求：1000-2000 USDT
- 结果：保持跟单关系

## 日志记录

### 成功日志
```
INFO - 开始清除带单员ID: 123 的无效跟单关系
INFO - 找到 5 个跟单用户，开始验证余额条件
INFO - 清除跟单关系 - 用户ID: 456, 用户名: user1, 余额: 500.00, 原因: 不满足跟单金额条件，已解锁账户
DEBUG - 已解锁用户ID: 456 的跟单账户锁定状态
INFO - 带单员ID: 123 的跟单关系清除完成，共清除 2 个关系
INFO - 一键平仓后清除了 2 个不满足条件的跟单关系，带单员ID: 123
```

### 错误日志
```
ERROR - 清除带单员ID: 123 的跟单关系失败
ERROR - 一键平仓后清除跟单关系失败，带单员ID: 123
```

## 安全机制

### 1. 事务保护
- 所有数据库操作都在事务中执行
- 异常时自动回滚，保证数据一致性

### 2. 异常隔离
- 清除跟单关系的失败不会影响平仓操作
- 详细的错误日志记录，便于问题排查

### 3. 数据验证
- 验证用户存在性和跟单状态
- 验证带单员配置的有效性
- 防止重复操作和数据不一致

## 测试覆盖

### 单元测试
- 余额不足的清除测试
- 余额超限的清除测试
- 无最高限制的测试
- 无跟单用户的测试
- 单个用户关系检查测试

### 集成测试
- 一键平仓后的自动清除
- 自动平仓后的自动清除
- 异常情况的处理

## 性能考虑

### 1. 查询优化
- 使用索引查询跟单用户
- 批量处理用户关系更新

### 2. 异步处理
- 清除操作不阻塞平仓流程
- 异常时记录日志但不影响主流程

### 3. 资源控制
- 合理的事务范围
- 及时释放数据库连接
