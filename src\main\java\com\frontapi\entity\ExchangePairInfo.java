package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.util.Date;

@Data
@TableName("exchange_pair_info")
public class ExchangePairInfo {
    @TableId
    private Long id;

    /** 交易所名称 */
    @TableField("exchange_name")
    private String exchangeName;

    /** 代币名称 */
    @TableField("token_name")
    private String tokenName;

    /** 交易对名称 */
    @TableField("pair_name")
    private String pairName;

    /** 是否启用(0:禁用,1:启用) */
    @TableField("is_enabled")
    private Integer isEnabled;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;

    /** logo地址 */
    @TableField("logo_url")
    private String logoUrl;

    /** 排序 */
    @TableField("sort")
    private Integer sort;

    /** api请求地址 */
    @TableField("api_url")
    private String apiUrl;

    /** 特殊处理(0:否,1:是) */
    @TableField("special_processing")
    private Integer specialProcessing;  

} 