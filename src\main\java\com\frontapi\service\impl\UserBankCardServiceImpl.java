package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.entity.UserBankCard;
import com.frontapi.exception.BusinessException;
import com.frontapi.mapper.UserBankCardMapper;
import com.frontapi.service.UserBankCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


@Service
@RequiredArgsConstructor
public class UserBankCardServiceImpl implements UserBankCardService {

    private final UserBankCardMapper userBankCardMapper;

    @Override
    public List<UserBankCard> getUserBankCards() {
        QueryWrapper<UserBankCard> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 1)
              .orderByDesc("create_time");
        return userBankCardMapper.selectList(wrapper);
    }

    @Override
    public boolean existsByUserIdAndCardNo(Long userId, String cardNumber) {
        return userBankCardMapper.existsByUserIdAndCardNo(userId, cardNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBankCard(Long userId, UserBankCard bankCard) {
        // 检查银行卡是否已存在
        if (existsByUserIdAndCardNo(userId, bankCard.getCardNumber())) {
            throw new BusinessException("该银行卡已绑定");
        }

        // 设置用户ID
        bankCard.setUserId(userId);
        bankCard.setStatus(1); // 设置状态为正常
        LocalDateTime now = LocalDateTime.now();
        bankCard.setUpdateTime(now);
        bankCard.setCreateTime(now);
        // 检查是否有其他默认卡
        if (Boolean.TRUE.equals(bankCard.getIsDefault())) {
            // 将其他卡片设置为非默认
            UserBankCard defaultCard = userBankCardMapper.selectOne(
                new QueryWrapper<UserBankCard>()
                    .eq("user_id", userId)
                    .eq("is_default", true)
            );
            if (defaultCard != null) {
                defaultCard.setIsDefault(false);
                userBankCardMapper.updateById(defaultCard);
            }
        }
        
        // 保存新卡片
        userBankCardMapper.insert(bankCard);
    }

    @Override
    public boolean unbindBankCard(Long cardId) {
        // 先查询银行卡是否存在
        UserBankCard bankCard = userBankCardMapper.selectById(cardId);
        if (bankCard == null) {
            throw new BusinessException("银行卡不存在");
        }

        // 直接从数据库中删除该记录
        int rows = userBankCardMapper.deleteById(cardId);
        return rows > 0;
    }

    @Override
    public List<UserBankCard> getByUserId(Long userId) {
        // 使用 userBankCardMapper 而不是 baseMapper
        QueryWrapper<UserBankCard> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
              .eq("status", 1)
              .orderByDesc("is_default")
              .orderByDesc("create_time");
        
        return userBankCardMapper.selectList(wrapper);
    }
} 