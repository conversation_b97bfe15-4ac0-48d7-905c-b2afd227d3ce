<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线图SSE推送演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .data-display {
            margin-top: 20px;
        }
        .pair-card {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .pair-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .pair-name {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .pair-price {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
        }
        .pair-change {
            font-size: 14px;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
        }
        .pair-change.positive {
            background: #d4edda;
            color: #155724;
        }
        .pair-change.negative {
            background: #f8d7da;
            color: #721c24;
        }
        .pair-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        .info-item {
            text-align: center;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        .info-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
        .kline-preview {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>K线图SSE推送演示</h1>
            <p>实时接收所有交易对的K线数据和行情信息</p>
        </div>

        <div class="controls">
            <button id="connectBtn" onclick="connectSSE()">连接SSE</button>
            <button id="disconnectBtn" onclick="disconnectSSE()" disabled>断开连接</button>
            <button onclick="getAllData()">获取所有数据</button>
        </div>

        <div id="status" class="status disconnected">
            状态: 未连接
        </div>

        <div class="data-display">
            <div id="pairsContainer">
                <p>等待连接...</p>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;
        let lastUpdateTime = null;

        function updateStatus(connected, message) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            isConnected = connected;
            
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = '状态: ' + message;
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '状态: ' + message;
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            try {
                eventSource = new EventSource('/api/market/kline/stream');
                
                eventSource.onopen = function(event) {
                    updateStatus(true, '已连接到K线数据流');
                };
                
                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        displayPairsData(data);
                        lastUpdateTime = new Date();
                    } catch (error) {
                        console.error('解析数据失败:', error);
                    }
                };
                
                eventSource.onerror = function(event) {
                    updateStatus(false, '连接错误');
                    console.error('SSE连接错误:', event);
                };
                
            } catch (error) {
                updateStatus(false, '连接失败: ' + error.message);
                console.error('连接SSE失败:', error);
            }
        }

        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            updateStatus(false, '已断开连接');
            document.getElementById('pairsContainer').innerHTML = '<p>等待连接...</p>';
        }

        function displayPairsData(pairsData) {
            const container = document.getElementById('pairsContainer');
            
            if (!Array.isArray(pairsData) || pairsData.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            let html = '';
            
            pairsData.forEach(pair => {
                const ticker = pair.ticker || {};
                const price = ticker.price || '--';
                const changeRate = ticker.changeRate || 0;
                const high = ticker.high || '--';
                const low = ticker.low || '--';
                const vol = ticker.vol || '--';
                const amount = ticker.amount || '--';
                
                const changeClass = changeRate > 0 ? 'positive' : changeRate < 0 ? 'negative' : '';
                const changeText = changeRate > 0 ? '+' + changeRate + '%' : changeRate + '%';
                
                // 格式化成交额
                const formattedAmount = formatAmount(amount);
                
                html += `
                    <div class="pair-card">
                        <div class="pair-header">
                            <div class="pair-name">${pair.label}</div>
                            <div class="pair-price">$${price}</div>
                        </div>
                        <div class="pair-change ${changeClass}">${changeText}</div>
                        <div class="pair-info">
                            <div class="info-item">
                                <div class="info-label">最高</div>
                                <div class="info-value">${high}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最低</div>
                                <div class="info-value">${low}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成交量</div>
                                <div class="info-value">${vol}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成交额</div>
                                <div class="info-value">${formattedAmount}</div>
                            </div>
                        </div>
                        <div class="kline-preview">
                            K线数据: ${pair.klineData ? Object.keys(pair.klineData).length : 0} 个时间周期
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // 显示更新时间
            if (lastUpdateTime) {
                const timeDiv = document.createElement('div');
                timeDiv.className = 'timestamp';
                timeDiv.textContent = '最后更新: ' + lastUpdateTime.toLocaleString('zh-CN');
                container.appendChild(timeDiv);
            }
        }

        function formatAmount(amount) {
            if (amount === '--' || amount === null || isNaN(amount)) {
                return '--';
            }
            const num = parseFloat(amount);
            if (num >= 100000000) {
                return (num / 100000000).toFixed(2) + '亿';
            }
            if (num >= 10000) {
                return (num / 10000).toFixed(2) + '万';
            }
            return num.toFixed(2);
        }

        async function getAllData() {
            try {
                const response = await fetch('/api/market/kline/all');
                const data = await response.json();
                
                if (data.data) {
                    displayPairsData(data.data);
                    lastUpdateTime = new Date();
                    updateStatus(false, '已获取所有数据');
                }
            } catch (error) {
                console.error('获取所有数据失败:', error);
                alert('获取数据失败: ' + error.message);
            }
        }

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html> 