# K线图数据SSE推送功能说明

## 功能概述

基于现有K线图页面的数据结构，创建了一个统一的SSE推送服务。系统会自动获取数据库中所有启用交易对的K线数据和行情信息，通过SSE实时推送给前端。

## 数据格式

### SSE推送的数据结构

```json
[
  {
    "symbol": "BTCUSDT",
    "label": "比特币/USDT", 
    "exchangeName": "Binance",
    "tokenName": "比特币",
    "logoUrl": "https://www.binance.com/static/images/coin/btc.png",
    "ticker": {
      "price": "46200.00",
      "changeRate": "2.5",
      "high": "46500.00", 
      "low": "45800.00",
      "vol": "1234.56",
      "amount": "56789012.34"
    },
    "klineData": {
      "1m": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...],
      "5m": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...],
      "15m": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...],
      "1h": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...],
      "4h": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...],
      "1d": [[1640995200000, "46200.00", "46250.00", "46150.00", "46225.00", "100.5"], ...]
    }
  }
]
```

### K线数据格式

每个K线数据包含6个值：
```javascript
[
  timestamp,  // 开盘时间戳 (毫秒)
  open,       // 开盘价
  high,       // 最高价  
  low,        // 最低价
  close,      // 收盘价
  volume      // 成交量
]
```

## API接口

### 1. SSE数据流

**接口**: `GET /api/market/kline/stream`

**说明**: 实时推送所有交易对的数据，每5秒更新一次

**使用示例**:
```javascript
const eventSource = new EventSource('/api/market/kline/stream');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    // data 是包含所有交易对数据的数组
    console.log('收到数据:', data);
};
```

### 2. 获取所有数据

**接口**: `GET /api/market/kline/all`

**说明**: 一次性获取所有交易对的完整数据

**返回格式**:
```json
{
  "data": [...],  // 交易对数据数组
  "timestamp": 1640995260000
}
```

## 前端集成示例

### 1. 连接SSE并处理数据

```javascript
// 连接SSE
const eventSource = new EventSource('/api/market/kline/stream');

eventSource.onopen = function(event) {
    console.log('SSE连接已建立');
};

eventSource.onmessage = function(event) {
    const pairsData = JSON.parse(event.data);
    
    // 处理每个交易对的数据
    pairsData.forEach(pair => {
        console.log('交易对:', pair.symbol);
        console.log('价格:', pair.ticker.price);
        console.log('涨跌幅:', pair.ticker.changeRate);
        
        // 获取特定时间周期的K线数据
        const kline1m = pair.klineData['1m'];
        const kline1h = pair.klineData['1h'];
        
        // 更新页面显示
        updatePairDisplay(pair);
    });
};

eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
};
```

### 2. 根据交易对名称获取数据

```javascript
function getPairData(pairsData, symbol) {
    return pairsData.find(pair => pair.symbol === symbol);
}

// 使用示例
const btcData = getPairData(pairsData, 'BTCUSDT');
if (btcData) {
    console.log('BTC价格:', btcData.ticker.price);
    console.log('BTC 1分钟K线:', btcData.klineData['1m']);
}
```

### 3. 更新K线图

```javascript
function updateKlineChart(pairData, interval = '1m') {
    const klineData = pairData.klineData[interval];
    if (klineData) {
        // 转换数据格式为ECharts需要的格式
        const chartData = klineData.map(item => [
            parseFloat(item[1]), // open
            parseFloat(item[4]), // close  
            parseFloat(item[3]), // low
            parseFloat(item[2])  // high
        ]);
        
        // 更新图表
        chart.setOption({
            series: [{
                data: chartData
            }]
        });
    }
}
```

## 与现有页面的集成

### 修改现有K线图页面

在现有的 `index.vue` 页面中，可以这样集成：

```javascript
// 在 mounted() 中添加
mounted() {
    this.connectSSE();
},

methods: {
    connectSSE() {
        this.eventSource = new EventSource('/api/market/kline/stream');
        
        this.eventSource.onmessage = (event) => {
            const pairsData = JSON.parse(event.data);
            
            // 根据当前交易对更新数据
            const currentPairData = pairsData.find(pair => 
                pair.symbol === this.currentSymbol.value
            );
            
            if (currentPairData) {
                this.updateFromSSE(currentPairData);
            }
        };
    },
    
    updateFromSSE(pairData) {
        // 更新ticker数据
        this.ticker = {
            price: pairData.ticker.price,
            changeRate: pairData.ticker.changeRate,
            high: pairData.ticker.high,
            low: pairData.ticker.low,
            vol: pairData.ticker.vol,
            amount: pairData.ticker.amount
        };
        
        // 更新K线数据
        const klineData = pairData.klineData[this.period] || pairData.klineData['1m'];
        if (klineData) {
            this.processKlineData(klineData);
        }
    }
}
```

## 测试

### 方式1: 直接访问演示页面
访问 `http://localhost:8094/kline-sse-demo.html` 查看演示页面。

### 方式2: 使用API接口测试
```bash
# 获取所有数据
curl http://localhost:8094/api/market/kline/all

# 或者使用浏览器直接访问
http://localhost:8094/api/market/kline/all
```

### 方式3: 使用Postman或其他工具测试SSE
1. 打开Postman
2. 创建GET请求: `http://localhost:8094/api/market/kline/stream`
3. 发送请求，观察实时数据推送

## 注意事项

1. **数据更新频率**: 每5秒更新一次所有交易对数据
2. **数据完整性**: 包含所有启用交易对的完整数据
3. **格式兼容**: 数据格式与现有页面完全兼容
4. **性能优化**: 只向有连接的客户端推送数据
5. **端口配置**: 应用运行在8094端口

## 优势

- ✅ **统一数据源**: 所有交易对数据统一推送
- ✅ **实时更新**: 5秒间隔自动更新
- ✅ **格式兼容**: 与现有页面数据结构完全一致
- ✅ **简单集成**: 前端只需要根据交易对名称筛选数据
- ✅ **减少请求**: 一个SSE连接获取所有数据 