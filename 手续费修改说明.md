# 手续费计算逻辑修改说明

## 修改概述

根据您的要求，已将盈利和亏损的手续费计算方式统一修改为：**成交数量 × 平仓价格 × 手续费率**，并确保手续费都从跟单账户中扣除。

## 修改的文件

### 1. SettlementServiceImpl.java

**文件路径：** `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`

#### 修改内容：

##### A. 跟单员盈利结算 (`processProfitSettlement` 方法)
- **原逻辑：** 手续费 = 盈利 × 手续费率
- **新逻辑：** 手续费 = 成交数量 × 平仓价格 × 手续费率
- **扣除方式：** 从跟单账户扣除手续费，并记录交易明细

```java
// 修改前
BigDecimal fee = profit.multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

// 修改后
BigDecimal positionAmount = followOrder.getPositionAmount(); // 成交数量
BigDecimal closePrice = followOrder.getClosePrice(); // 平仓价格
BigDecimal fee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
```

##### B. 跟单员亏损结算 (`processLossSettlement` 方法)
- **原逻辑：** 手续费 = 保证金 × 手续费率
- **新逻辑：** 手续费 = 成交数量 × 平仓价格 × 手续费率
- **扣除方式：** 从跟单账户扣除手续费，并记录交易明细

```java
// 修改前
BigDecimal fee = marginAmount.multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

// 修改后
BigDecimal positionAmount = followOrder.getPositionAmount(); // 成交数量
BigDecimal closePrice = followOrder.getClosePrice(); // 平仓价格
BigDecimal fee = positionAmount.multiply(closePrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
```

##### C. 带单员盈利结算 (`processLeaderProfitSettlement` 方法)
- **原逻辑：** 手续费 = 盈利 × 手续费率
- **新逻辑：** 手续费 = 成交数量 × 平仓价格 × 手续费率
- **扣除方式：** 从带单员跟单账户扣除手续费，并记录交易明细

##### D. 新增带单员亏损结算 (`processLeaderLossSettlement` 方法)
- **新增方法：** 处理带单员亏损情况的手续费扣除
- **计算逻辑：** 手续费 = 成交数量 × 平仓价格 × 手续费率
- **扣除方式：** 从带单员跟单账户扣除手续费，并记录交易明细

## 关键改进点

### 1. 统一的手续费计算公式
所有订单（盈利/亏损，跟单员/带单员）都使用相同的计算公式：
```
手续费 = 成交数量 × 平仓价格 × 手续费率 ÷ 100
```

### 2. 统一的扣除方式
- 所有手续费都从跟单账户中扣除
- 使用 `deductFeeFromUser` 方法统一处理
- 自动记录交易明细，类型为"手续费扣除"

### 3. 完整的日志记录
每次手续费计算都会记录详细信息：
- 成交数量
- 平仓价格
- 手续费率
- 计算出的手续费金额

## 数据字段说明

### DeliveryOrder 实体中的关键字段：
- `positionAmount`: 成交数量（持仓量）
- `closePrice`: 平仓价格
- `profit`: 盈利/亏损金额
- `marginAmount`: 保证金金额

## 计算示例

### 示例1：盈利情况
- 成交数量：0.5 BTC
- 平仓价格：34,000 USDT
- 手续费率：10%
- **手续费 = 0.5 × 34,000 × 10% = 1,700 USDT**

### 示例2：亏损情况
- 成交数量：0.3 BTC
- 平仓价格：32,000 USDT
- 手续费率：10%
- **手续费 = 0.3 × 32,000 × 10% = 960 USDT**

## 注意事项

1. **期权订单未修改**：期权订单（FuturesOptionOrder）的手续费计算逻辑保持不变，因为期权订单没有"成交数量"概念，仍使用订单金额计算手续费。

2. **交易记录**：所有手续费扣除都会自动生成交易记录，便于用户查询和系统审计。

3. **账户类型**：手续费扣除记录的账户类型为2（跟单账户）。

4. **异常处理**：如果跟单账户余额不足以支付手续费，会抛出异常并回滚事务。

## 新增功能：开仓佣金分配

### 功能描述
在跟单成功（订单创建完成）后，根据每个人的订单（包括带单人的订单）计算开仓手续费，并按照这个手续费进行佣金分配。

### 实现方式

#### 1. 新增方法
- **文件：** `SettlementServiceImpl.java`
- **方法：** `processOpenCommissionDistribution(DeliveryOrder order)`

#### 2. 计算公式
```
开仓手续费 = 成交数量 × 开仓价格 × 手续费率
```

#### 3. 调用时机
- 在 `DeliveryOrderServiceImpl.createOrderWithBalance()` 方法中
- 订单创建成功后立即调用
- 适用于带单员订单和跟单员订单

#### 4. 重要特点
- **只计算佣金分配**：开仓时不扣除任何费用，只根据开仓手续费计算佣金
- **使用开仓价格**：与平仓时使用平仓价格不同，开仓佣金使用开仓价格
- **异常处理**：佣金分配失败不影响订单创建流程

### 代码示例

<augment_code_snippet path="src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java" mode="EXCERPT">
```java
// 计算开仓手续费：成交数量 * 开仓价格 * 手续费率
BigDecimal positionAmount = order.getPositionAmount(); // 成交数量
BigDecimal openPrice = order.getOpenPrice(); // 开仓价格
BigDecimal openFee = positionAmount.multiply(openPrice).multiply(feeRate).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

// 使用开仓手续费进行佣金分配（只计算佣金，不扣除其他费用）
processCommissionDistribution(order.getUserId(), openFee, order.getId());
```
</augment_code_snippet>

### 计算示例

#### 示例1：带单员开仓
- 成交数量：1.0 BTC
- 开仓价格：35,000 USDT
- 手续费率：10%
- **开仓手续费 = 1.0 × 35,000 × 10% = 3,500 USDT**
- 用于佣金分配的基数：3,500 USDT

#### 示例2：跟单员开仓
- 成交数量：0.3 BTC
- 开仓价格：35,000 USDT
- 手续费率：10%
- **开仓手续费 = 0.3 × 35,000 × 10% = 1,050 USDT**
- 用于佣金分配的基数：1,050 USDT

## 测试建议

建议在测试环境中验证以下场景：

### 平仓相关测试
1. 跟单员盈利订单的手续费计算和扣除
2. 跟单员亏损订单的手续费计算和扣除
3. 带单员盈利订单的手续费计算和扣除
4. 带单员亏损订单的手续费计算和扣除
5. 手续费扣除的交易记录生成
6. 跟单账户余额不足时的异常处理

### 开仓相关测试
7. 带单员开仓佣金分配计算
8. 跟单员开仓佣金分配计算
9. 开仓佣金分配的推荐链处理
10. 开仓佣金分配失败时的异常处理
11. 验证开仓时不扣除任何费用，只分配佣金
