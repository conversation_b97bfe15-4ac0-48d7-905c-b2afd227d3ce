package com.frontapi.service;

import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SSE推送服务
 * 管理每个用户的SseEmitter，实现注册、推送和在线用户管理
 */
@Service
public class SsePushService {
    // 用户ID -> SseEmitter
    private final Map<Long, SseEmitter> emitterMap = new ConcurrentHashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(SsePushService.class);

    /**
     * 为用户注册SseEmitter
     */
    public SseEmitter createEmitterForUser(Long userId) {
        SseEmitter emitter = new SseEmitter(0L); // 永不超时
        emitterMap.put(userId, emitter);
        logger.info("注册SseEmitter, userId={}, emitter={}", userId, emitter);
        emitter.onCompletion(() -> {
            emitterMap.remove(userId);
            logger.info("SseEmitter完成, userId={}", userId);
        });
        emitter.onTimeout(() -> {
            emitterMap.remove(userId);
            logger.info("SseEmitter超时, userId={}", userId);
        });
        emitter.onError(e -> {
            emitterMap.remove(userId);
            logger.info("SseEmitter异常, userId={}, error={}", userId, e.getMessage());
        });
        return emitter;
    }

    /**
     * 推送数据给指定用户
     */
    public void pushHoldProfit(Long userId, Object profitList) {
        SseEmitter emitter = emitterMap.get(userId);
        logger.info("推送数据, userId={}, emitter={}, data={}", userId, emitter, profitList);
        if (emitter != null) {
            try {
                emitter.send(profitList);
                logger.info("推送成功, userId={}", userId);
            } catch (Exception e) {
                emitterMap.remove(userId);
                logger.error("推送失败, userId={}, error={}", userId, e.getMessage());
            }
        } else {
            logger.warn("未找到SseEmitter, userId={}", userId);
        }
    }

    /**
     * 获取所有在线用户ID
     */
    public Set<Long> getOnlineUserIds() {
        return emitterMap.keySet();
    }
} 