package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.RechargeRecord;
import com.frontapi.mapper.RechargeRecordMapper;
import com.frontapi.service.RechargeRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RechargeRecordServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord> 
    implements RechargeRecordService {

    @Override
    public Page<RechargeRecord> getRechargeList(Long userId, int page, int size) {
        Page<RechargeRecord> pageInfo = new Page<>(page, size);
        return baseMapper.selectRechargeRecordPage(pageInfo, userId);
    }
} 