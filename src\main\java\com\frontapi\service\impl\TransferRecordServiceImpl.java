package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.entity.TransferRecord;
import com.frontapi.mapper.TransferRecordMapper;
import com.frontapi.service.TransferRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor 
public class TransferRecordServiceImpl implements TransferRecordService {

    @Autowired
    private TransferRecordMapper transferRecordMapper;

    @Override
    public Page<TransferRecord> getTransferInList(Long userId, Integer page, Integer pageSize) {
        Page<TransferRecord> pageInfo = new Page<>(page, pageSize);
        LambdaQueryWrapper<TransferRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TransferRecord::getToUserId, userId)
               .orderByDesc(TransferRecord::getCreateTime);
        return transferRecordMapper.selectPage(pageInfo, wrapper);
    }

    @Override
    public Page<TransferRecord> getTransferOutList(Long userId, Integer page, Integer pageSize) {
        Page<TransferRecord> pageInfo = new Page<>(page, pageSize);
        LambdaQueryWrapper<TransferRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TransferRecord::getFromUserId, userId)
               .orderByDesc(TransferRecord::getCreateTime);
        return transferRecordMapper.selectPage(pageInfo, wrapper);
    }
}