package com.frontapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.common.Result;
import com.frontapi.entity.WithdrawRecord;
import com.frontapi.service.WithdrawRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/withdraw-record")
@RequiredArgsConstructor
public class WithdrawRecordController {
    private final WithdrawRecordService withdrawRecordService;
    private final UserService userService;

    @GetMapping("/list")
    public Result<IPage<WithdrawRecord>> list(@RequestParam(defaultValue = "1") Integer page,
                                              @RequestParam(defaultValue = "10") Integer size) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }
        Page<WithdrawRecord> pageParam = new Page<>(page, size);
        IPage<WithdrawRecord> result = withdrawRecordService.lambdaQuery()
                .eq(WithdrawRecord::getUserId, currentUser.getId())
                .orderByDesc(WithdrawRecord::getCreateTime)
                .page(pageParam);
        return Result.ok(result);
    }

    @GetMapping("/{id}")
    public Result<WithdrawRecord> detail(@PathVariable Long id) {
        WithdrawRecord record = withdrawRecordService.getById(id);
        if (record == null) {
            return Result.fail("记录不存在");
        }
        return Result.ok(record);
    }
} 