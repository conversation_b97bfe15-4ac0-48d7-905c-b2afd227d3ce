package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("commission_record")
public class CommissionRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long userId;
    private String username;
    private String phone;
    private BigDecimal commissionAmount;
    private Integer commissionType;  // 赠送类型(1.购买赠送 2.推广赠送 3.培育赠送 4.管理赠送)
    private LocalDateTime releaseTime;
    private String remark;
    private Integer releaseStatus;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 