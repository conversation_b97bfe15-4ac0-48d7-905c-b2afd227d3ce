package com.frontapi.service;

import com.frontapi.entity.DeliveryOrder;

/**
 * 自动平仓服务接口
 */
public interface AutoCloseService {
    
    /**
     * 启动自动平仓监控
     */
    void startAutoCloseMonitoring();
    
    /**
     * 停止自动平仓监控
     */
    void stopAutoCloseMonitoring();
    
    /**
     * 检查单个订单是否需要自动平仓
     * @param order 订单信息
     * @param currentPrice 当前价格
     * @return true-需要平仓，false-不需要平仓
     */
    boolean shouldAutoClose(DeliveryOrder order, java.math.BigDecimal currentPrice);
    
    /**
     * 获取自动平仓原因
     * @param order 订单信息
     * @param currentPrice 当前价格
     * @return 平仓原因（止盈/止损）
     */
    String getAutoCloseReason(DeliveryOrder order, java.math.BigDecimal currentPrice);
}
