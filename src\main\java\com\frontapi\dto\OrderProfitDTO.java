package com.frontapi.dto;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 持仓订单盈利推送DTO
 * 用于SSE实时推送每笔持仓订单的盈利、盈利百分比及公司备注到前端
 */
@Data
@AllArgsConstructor
public class OrderProfitDTO {
    /** 订单ID */
    private Long orderId;
    /** 当前盈利 */
    private BigDecimal profit;
    /** 当前盈利百分比 */
    private BigDecimal percent;
    /** 成交状态（待成交/已成交） */
    private int status;
    /** 公司备注 */
    private String companyRemark;
    /** 结算时间 */
    private Date settleTime;

    /**
     * 收益 = (currentPrice - openPrice) × amount
     */
    public BigDecimal getProfit() {
        return profit;
    }

    /**
     * 收益率 = (currentPrice - openPrice) / openPrice × 100%（买入）
     *         (openPrice - currentPrice) / openPrice × 100%（卖出）
     */
    public BigDecimal getProfitPercent() {
        return percent;
    }
} 