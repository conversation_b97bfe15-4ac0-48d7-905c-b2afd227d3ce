package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@TableName("transfer_record")
public class TransferRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("from_user_id")
    private Long fromUserId;
    
    @TableField("from_username")
    private String fromUsername;
    
    @TableField("to_user_id")
    private Long toUserId;
    
    @TableField("to_username")
    private String toUsername;
    
    private BigDecimal amount;
    
    private BigDecimal fee;
    
    @TableField("real_amount")
    private BigDecimal realAmount;
    
    private Integer status;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 