package com.frontapi.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class UploadPathConfig {
    @Value("${upload.env}")
    private String env;

    @Value("${upload.path-local}")
    private String pathLocal;

    @Value("${upload.path-prod}")
    private String pathProd;

    public String getUploadPath() {
        if ("prod".equalsIgnoreCase(env)) {
            return pathProd;
        } else {
            return pathLocal;
        }
    }
} 