<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.CommissionRecordMapper">
    
    <select id="getCommissionStats" resultType="com.frontapi.vo.CommissionStatsVO">
        SELECT 
            COALESCE(SUM(c.commission_amount), 0) as totalCommission,
            COALESCE(SUM(CASE WHEN c.commission_type = '1' THEN c.commission_amount ELSE 0 END), 0) as purchaseCommission,
            COALESCE(SUM(CASE WHEN c.commission_type = '2' THEN c.commission_amount ELSE 0 END), 0) as promotionCommission,
            COALESCE(SUM(CASE WHEN c.commission_type = '3' THEN c.commission_amount ELSE 0 END), 0) as cultivateCommission,
            COALESCE(SUM(CASE WHEN c.commission_type = '4' THEN c.commission_amount ELSE 0 END), 0) as manageCommission,
            (
                COALESCE((
                    SELECT SUM(amount) 
                    FROM withdraw_record 
                    WHERE user_id = #{userId}
                ), 0) + 
                COALESCE((
                    SELECT SUM(amount) 
                    FROM transfer_record 
                    WHERE from_user_id = #{userId}
                ), 0)
            ) as transferAmount
        FROM commission_record c
        WHERE c.user_id = #{userId}
    </select>
    
    <select id="sumCommissionAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(commission_amount), 0)
        FROM commission_record
        WHERE user_id = #{userId}
        AND commission_type = #{type}
        AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="sumTodayCommission" resultType="java.math.BigDecimal">
      SELECT COALESCE(
        (SELECT SUM(profit) FROM delivery_order
         WHERE user_id = #{userId}
           AND profit_status = 1
           AND create_time BETWEEN #{startTime} AND #{endTime}), 0) +
        COALESCE(
        (SELECT SUM(profit) FROM futures_option_order
         WHERE user_id = #{userId}
           AND profit_status = 1
           AND create_time BETWEEN #{startTime} AND #{endTime}), 0)
    </select>

    <select id="getProfitSummary" resultType="com.frontapi.vo.ProfitSummaryVO">
        SELECT
            COALESCE(SUM(CASE WHEN commission_type = 1 THEN commission_amount ELSE 0 END), 0) as totalCommission,
            COALESCE(SUM(CASE WHEN commission_type = 2 THEN commission_amount ELSE 0 END), 0) as totalProfit
        FROM commission_record
        WHERE user_id = #{userId}
    </select>

</mapper>