package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.AccountTransferRecord;
import com.frontapi.mapper.AccountTransferRecordMapper;
import com.frontapi.service.AccountTransferRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AccountTransferRecordServiceImpl extends ServiceImpl<AccountTransferRecordMapper, AccountTransferRecord> implements AccountTransferRecordService {

    @Override
    public Page<AccountTransferRecord> getTransferRecords(Long userId, int page, int size) {
        Page<AccountTransferRecord> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<AccountTransferRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountTransferRecord::getUserId, userId)
                   .orderByDesc(AccountTransferRecord::getCreateTime);
        return this.page(pageParam, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTransferRecord(AccountTransferRecord record) {
        return this.save(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransferStatus(Long recordId, Integer status, String remark) {
        AccountTransferRecord record = this.getById(recordId);
        if (record != null) {
            record.setStatus(status);
            record.setRemark(remark);
            return this.updateById(record);
        }
        return false;
    }
} 