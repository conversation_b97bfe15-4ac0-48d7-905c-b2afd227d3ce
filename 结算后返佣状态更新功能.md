# 结算后返佣状态更新功能

## 🎯 **功能需求**

在平仓结算完成后，根据盈亏情况自动更新所有相关订单的 `rebate_status` 字段：
- **盈利**: `rebate_status = 2` (已返)
- **亏损**: `rebate_status = 1` (未返)

## 📊 **字段说明**

### rebate_status（返佣状态）
- **1**: 未返
- **2**: 已返

## 🔧 **实现方案**

### 1. 数据库层修改

#### 1.1 新增查询方法
**文件**: `src/main/java/com/frontapi/mapper/DeliveryOrderMapper.java`

```java
/**
 * 根据带单员订单ID查询所有相关订单ID（包括带单员订单和跟单订单）
 */
@Select("SELECT id FROM delivery_order WHERE " +
        "(id = #{leaderOrderId}) OR " +
        "(leader_id = (SELECT leader_id FROM delivery_order WHERE id = #{leaderOrderId}) " +
        "AND DATE(open_time) = DATE((SELECT open_time FROM delivery_order WHERE id = #{leaderOrderId})) " +
        "AND symbol = (SELECT symbol FROM delivery_order WHERE id = #{leaderOrderId}))")
List<Long> getRelatedOrderIds(@Param("leaderOrderId") Long leaderOrderId);
```

#### 1.2 新增批量更新方法
```java
/**
 * 批量更新订单的返佣状态
 */
@Update("<script>" +
        "UPDATE delivery_order SET rebate_status = #{rebateStatus}, update_time = NOW() " +
        "WHERE id IN " +
        "<foreach collection='orderIds' item='orderId' open='(' separator=',' close=')'>" +
        "#{orderId}" +
        "</foreach>" +
        "</script>")
int batchUpdateRebateStatus(@Param("orderIds") List<Long> orderIds, @Param("rebateStatus") Integer rebateStatus);
```

#### 1.3 新增单个更新方法
```java
/**
 * 更新单个订单的返佣状态
 */
@Update("UPDATE delivery_order SET rebate_status = #{rebateStatus}, update_time = NOW() WHERE id = #{orderId}")
int updateRebateStatus(@Param("orderId") Long orderId, @Param("rebateStatus") Integer rebateStatus);
```

### 2. 业务逻辑层修改

#### 2.1 单个订单返佣状态更新
**文件**: `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`

```java
/**
 * 根据盈亏情况更新返佣状态
 * 盈利：rebate_status = 2 (已返)
 * 亏损：rebate_status = 1 (未返)
 */
private void updateRebateStatusByProfit(DeliveryOrder order) {
    BigDecimal profit = order.getProfit();
    int rebateStatus;
    String statusDesc;
    
    if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0) {
        // 盈利：设置为已返
        rebateStatus = 2;
        statusDesc = "已返";
    } else {
        // 亏损或持平：设置为未返
        rebateStatus = 1;
        statusDesc = "未返";
    }
    
    int updateResult = deliveryOrderMapper.updateRebateStatus(order.getId(), rebateStatus);
    log.info("更新订单返佣状态成功，订单ID: {}, 盈利: {}, 返佣状态: {}",
            order.getId(), profit, statusDesc);
}
```

#### 2.2 批量订单返佣状态更新
```java
/**
 * 批量更新相关订单的返佣状态（带单员订单触发）
 * 根据带单员订单的盈亏情况，更新所有相关订单的返佣状态
 */
private void updateRelatedOrdersRebateStatus(DeliveryOrder leaderOrder) {
    BigDecimal profit = leaderOrder.getProfit();
    int rebateStatus = (profit != null && profit.compareTo(BigDecimal.ZERO) > 0) ? 2 : 1;
    String statusDesc = (rebateStatus == 2) ? "已返" : "未返";
    
    // 查询所有相关订单ID
    List<Long> relatedOrderIds = deliveryOrderMapper.getRelatedOrderIds(leaderOrder.getId());
    if (relatedOrderIds != null && !relatedOrderIds.isEmpty()) {
        // 批量更新返佣状态
        int updateResult = deliveryOrderMapper.batchUpdateRebateStatus(relatedOrderIds, rebateStatus);
        log.info("批量更新相关订单返佣状态完成，带单员订单ID: {}, 盈利: {}, 返佣状态: {}, 相关订单数: {}, 影响订单数: {}",
                leaderOrder.getId(), profit, statusDesc, relatedOrderIds.size(), updateResult);
    }
}
```

### 3. 调用时机

#### 3.1 跟单订单结算完成后
**位置**: `processFollowOrderSettlement` 方法
```java
// 更新订单结算状态为已结算
updateOrderSettlementStatus(followOrder.getId(), 2);

// 根据盈亏情况更新返佣状态
updateRebateStatusByProfit(followOrder);

log.info("跟单订单结算完成，订单ID: {}", followOrder.getId());
```

#### 3.2 带单员订单结算完成后
**位置**: `processLeaderOrderSettlement` 方法
```java
// 更新订单结算状态为已结算
updateOrderSettlementStatus(leaderOrder.getId(), 2);

// 批量更新相关订单的返佣状态（包括带单员和所有跟单员）
updateRelatedOrdersRebateStatus(leaderOrder);

log.info("带单员订单结算完成，订单ID: {}", leaderOrder.getId());
```

## 🔄 **执行流程**

### 跟单订单结算流程
```
1. 平仓操作 → 设置 status=2, is_settlement=1
2. 资金结算 → 处理保证金、盈利分配、手续费
3. 状态更新 → 设置 is_settlement=2
4. 返佣状态更新 → 根据盈亏设置 rebate_status
```

### 带单员订单结算流程
```
1. 平仓操作 → 设置 status=2, is_settlement=1
2. 资金结算 → 处理保证金、盈利分配、手续费
3. 状态更新 → 设置 is_settlement=2
4. 批量返佣状态更新 → 根据盈亏批量设置所有相关订单的 rebate_status
5. 处理跟单订单 → 重复上述流程（但跟单订单只更新自己的状态）
```

## 📊 **更新逻辑**

### 盈利情况
```
if (profit > 0) {
    rebate_status = 2;  // 已返
}
```

### 亏损或持平情况
```
if (profit <= 0) {
    rebate_status = 1;  // 未返
}
```

## 🎯 **关键特性**

### 1. 智能判断
- 自动根据订单盈亏情况设置返佣状态
- 支持盈利、亏损、持平三种情况

### 2. 批量处理
- 带单员订单结算时，一次性更新所有相关订单
- 提高处理效率，保证数据一致性

### 3. 详细日志
- 记录每次更新的详细信息
- 包括订单ID、盈利金额、返佣状态、影响订单数等

### 4. 异常处理
- 完善的异常捕获和日志记录
- 确保主流程不受影响

## 📈 **预期效果**

### 1. 自动化管理
- ✅ 结算完成后自动更新返佣状态
- ✅ 无需人工干预

### 2. 数据一致性
- ✅ 同一批次的订单返佣状态保持一致
- ✅ 状态与实际盈亏情况匹配

### 3. 业务透明度
- ✅ 清晰的状态标识
- ✅ 便于业务人员查询和管理

### 4. 系统可靠性
- ✅ 完善的异常处理机制
- ✅ 详细的操作日志记录

## 🧪 **测试验证**

### 测试场景1：跟单订单盈利
- **预期**: 订单 `rebate_status` 更新为 2
- **验证**: 检查数据库字段和日志记录

### 测试场景2：跟单订单亏损
- **预期**: 订单 `rebate_status` 更新为 1
- **验证**: 检查数据库字段和日志记录

### 测试场景3：带单员订单盈利
- **预期**: 所有相关订单 `rebate_status` 批量更新为 2
- **验证**: 检查带单员订单和所有跟单订单的状态

### 测试场景4：带单员订单亏损
- **预期**: 所有相关订单 `rebate_status` 批量更新为 1
- **验证**: 检查带单员订单和所有跟单订单的状态

这个功能确保了返佣状态能够准确反映订单的盈亏情况，为后续的返佣处理提供了可靠的数据基础。
