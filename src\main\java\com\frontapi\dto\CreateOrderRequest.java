package com.frontapi.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CreateOrderRequest {
    private String symbol;           // 交易对，如 BTCUSDT
    private Integer direction;       // 买涨买跌 (1:买涨, 2:买跌)
    private Integer leverage;        // 杠杆倍数 (5 或 10)
    private BigDecimal quantity;     // 购买数量
    private BigDecimal takeProfit;   // 止盈价格 (必填)
    private BigDecimal stopLoss;     // 止损价格 (必填)
}
