package com.frontapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("notice")
public class Notice {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String title;
    
    private String content;
    
    private Integer noticeType;  // 公告类型(1:重要公告,2:普通公告,3:系统公告,4:活动公告,5:维护公告)
    
    private Integer sort;  // 排序号
    
    private Integer status;  // 状态(0:禁用,1:正常)
    
    private Integer isTop;  // 是否置顶(0:否,1:是)
    
    private LocalDateTime publishTime;  // 发布时间
    
    private String createBy;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    private String updateBy;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 