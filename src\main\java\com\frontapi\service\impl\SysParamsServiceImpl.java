package com.frontapi.service.impl;

import com.frontapi.entity.SysParams;
import com.frontapi.entity.TransferWithdrawParamsVO;
import com.frontapi.mapper.SysParamsMapper;
import com.frontapi.service.SysParamsService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service
public class SysParamsServiceImpl implements SysParamsService {
    @Resource
    private SysParamsMapper sysParamsMapper;

    @Override
    public SysParams getSysParams() {
        // 这里只是示例，实际请根据你的业务逻辑获取参数
        return sysParamsMapper.selectOne(null);
    }

    @Override
    public TransferWithdrawParamsVO getTransferWithdrawParams() {
        SysParams sysParams = getSysParams();
        if (sysParams == null) {
            return null;
        }
        TransferWithdrawParamsVO vo = new TransferWithdrawParamsVO();
        vo.setMinTransfer(sysParams.getMinTransfer());
        vo.setMaxTransfer(sysParams.getMaxTransfer());
        vo.setTransferFee(sysParams.getTransferFee());
        vo.setEnableTransfer(sysParams.getEnableTransfer());
        vo.setMinWithdraw(sysParams.getMinWithdraw());
        vo.setMaxWithdraw(sysParams.getMaxWithdraw());
        vo.setMaxAutoWithdraw(sysParams.getMaxAutoWithdraw());
        vo.setWithdrawFee(sysParams.getWithdrawFee());
        vo.setEnableWithdraw(sysParams.getEnableWithdraw());
        vo.setAutoWithdraw(sysParams.getAutoWithdraw());
        vo.setEnableInternalTransfer(sysParams.getEnableInternalTransfer());
        return vo;
    }
} 