-2025-07-28 09:02:12.916 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 记录交易明细成功，用户ID: 5, 类型: 下单扣除保证金, 金额: -952.70497459
-2025-07-28 09:02:12.916 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始处理开仓手续费扣除和佣金分配，订单ID: 51, 用户ID: 5
-2025-07-28 09:02:12.917 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开仓手续费计算 - 订单ID: 51, 原始成交数量: 0.03999992, 格式化成交数量: 0.040, 开仓价格: 119088.36000000, 手续费率: 0.04%, 开仓手续费: 1.9054
-2025-07-28 09:02:12.918 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 5, 金额: -1.9054, 类型: 手续费扣除, 账户类型: 2
-2025-07-28 09:02:12.920 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 5, 金额: -1.9054, 类型: 手续费扣除, 备注: 订单51手续费
-2025-07-28 09:02:12.920 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 扣除手续费成功，用户ID: 5, 金额: 1.9054, 订单ID: 51
-2025-07-28 09:02:12.920 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始处理佣金分配，用户ID: 5, 总佣金: 1.9054, 订单ID: 51
-2025-07-28 09:02:12.922 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第1层推荐人: ID=1, 佣金比例=5.00, 一键跟单=0
-2025-07-28 09:02:12.922 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第1层推荐人有佣金比例但无一键跟单，累积佣金比例: 5.00
-2025-07-28 09:02:12.922 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 佣金分配完成，最终累积佣金比例: 5.00
-2025-07-28 09:02:12.922 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开仓手续费扣除和佣金分配完成，订单ID: 51, 手续费: 1.9054
-2025-07-28 09:02:12.922 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 订单状态更新成功，订单ID: 51 从开仓处理中更新为持仓中
-2025-07-28 09:02:12.923 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 用户5下单成功，扣除保证金: 952.70497459, 订单ID: 51
-2025-07-28 09:02:12.923 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 为用户ID: 5 创建跟单订单成功，数量: 0.03999992, 保证金: 952.70497459
-2025-07-28 09:02:12.923 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 跟单计算 - 带单员数量: 0.2, 开仓价格: 119088.36000000, 订单价值: 23817.672000000, 原始余额: 9791.42630, 实际投入比例: 243.25%, 跟单余额: 1468.70837, 跟单金额: 3572.6372370955197, 跟单数量: 0.02999989
-2025-07-28 09:02:12.926 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 记录交易明细成功，用户ID: 7, 类型: 下单扣除保证金, 金额: -714.52754006
-2025-07-28 09:02:12.926 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始处理开仓手续费扣除和佣金分配，订单ID: 52, 用户ID: 7
-2025-07-28 09:02:12.927 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开仓手续费计算 - 订单ID: 52, 原始成交数量: 0.02999989, 格式化成交数量: 0.030, 开仓价格: 119088.36000000, 手续费率: 0.04%, 开仓手续费: 1.4291
-2025-07-28 09:02:12.928 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 7, 金额: -1.4291, 类型: 手续费扣除, 账户类型: 2
-2025-07-28 09:02:12.929 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 7, 金额: -1.4291, 类型: 手续费扣除, 备注: 订单52手续费
-2025-07-28 09:02:12.929 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 扣除手续费成功，用户ID: 7, 金额: 1.4291, 订单ID: 52
-2025-07-28 09:02:12.929 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开始处理佣金分配，用户ID: 7, 总佣金: 1.4291, 订单ID: 52
-2025-07-28 09:02:12.930 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第1层推荐人: ID=6, 佣金比例=20.00, 一键跟单=0
-2025-07-28 09:02:12.930 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第1层推荐人有佣金比例但无一键跟单，累积佣金比例: 20.00
-2025-07-28 09:02:12.931 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第2层推荐人: ID=5, 佣金比例=15.00, 一键跟单=1
-2025-07-28 09:02:12.931 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : === 开始记录收益明细 ===
-2025-07-28 09:02:12.932 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 用户ID: 5, 金额: 0.5002, 类型: 1 (2=收益), 备注: 订单52第2层推荐佣金
-2025-07-28 09:02:12.932 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 获取用户邮箱: <EMAIL>
-2025-07-28 09:02:12.932 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 执行数据库插入 - commission_record 表
-2025-07-28 09:02:13.032 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 数据库插入结果 - 影响行数: 1
-2025-07-28 09:02:13.032 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : ✅ 记录收益明细成功 - 用户ID: 5, 金额: 0.5002, 类型: 1, 备注: 订单52第2层推荐佣金
-2025-07-28 09:02:13.032 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 明细记录ID: 63
-2025-07-28 09:02:13.032 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 成功发放佣金给用户5, 金额: 0.5002, 层级: 2, 订单ID: 52
-2025-07-28 09:02:13.032 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第2层推荐人有一键跟单，发放佣金后累积归零
-2025-07-28 09:02:13.033 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第3层推荐人: ID=1, 佣金比例=5.00, 一键跟单=0
-2025-07-28 09:02:13.034 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 第3层推荐人有佣金比例但无一键跟单，累积佣金比例: 5.00
-2025-07-28 09:02:13.034 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 佣金分配完成，最终累积佣金比例: 5.00
-2025-07-28 09:02:13.034 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 开仓手续费扣除和佣金分配完成，订单ID: 52, 手续费: 1.4291
-2025-07-28 09:02:13.035 - INFO 11588 --- [io-8094-exec-12] c.f.service.impl.SettlementServiceImpl : 订单状态更新成功，订单ID: 52 从开仓处理中更新为持仓中
-2025-07-28 09:02:13.035 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 用户7下单成功，扣除保证金: 714.52754006, 订单ID: 52
-2025-07-28 09:02:13.035 - INFO 11588 --- [io-8094-exec-12] c.f.s.impl.DeliveryOrderServiceImpl : 为用户ID: 7 创建跟单订单成功，数量: 0.02999989, 保证金: 714.52754006
-2025-07-28 09:02:13.121 - INFO 11588 --- [io-8094-exec-12] c.f.controller.CopyOrderController : 带单员ID: 2 下单成功，已触发跟单逻辑，原始余额: 9791.42630
-2025-07-28 09:03:03.241 - INFO 11588 --- [io-8094-exec-12] c.f.c.FuturesOptionOrderController : SSE连接请求, userId=2
-2025-07-28 09:03:03.241 - INFO 11588 --- [io-8094-exec-12] com.frontapi.service.SsePushService : 注册SseEmitter, userId=2, emitter=SseEmitter@79e688ed
-2025-07-28 09:03:03.241 - INFO 11588 --- [io-8094-exec-12] c.f.c.FuturesOptionOrderController : SseEmitter注册完成, userId=2, emitter=SseEmitter@79e688ed
-2025-07-28 09:08:39.558 - INFO 11588 --- [MessageBroker-6] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 16, active threads = 2, queued tasks = 3, completed tasks = 67638]
-2025-07-28 09:09:43.787 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.AutoCloseServiceImpl : 触发自动平仓条件，订单ID: 50, 交易对: BTCUSDT, 当前价格: 119337.03000000, 原因: 止盈平仓
-2025-07-28 09:09:43.883 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 开始获取BTCUSDT的实时价格
-2025-07-28 09:09:43.883 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : Redis查询键: binance:ticker:BTCUSDT
-2025-07-28 09:09:43.884 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : Redis中现有的价格键数量: 13
-2025-07-28 09:09:43.884 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 前5个价格键示例: [binance:ticker:SHIBUSDT, binance:ticker:TRXUSDT, binance:ticker:ETHUSDT, binance:ticker:SUIUSDT, binance:ticker:DOTUSDT]
-2025-07-28 09:09:43.884 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试获取BTCUSDT的实时价格，Redis键: binance:ticker:BTCUSDT
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试成功，获取BTCUSDT实时价格: 119337.03000000
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : === 开始计算订单盈利 ===
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单ID: 50, 用户ID: 2, 交易对: BTCUSDT
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单方向: 1 (1=买涨, 2=买跌)
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 开仓价格: 119088.36000000, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 持仓数量: 0.200
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 买涨计算: (119337.03000000 - 119088.36000000) × 0.200 = 49.73400000000
-2025-07-28 09:09:43.885 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单盈利: 49.73400000000, 盈利状态: 1(盈利)
-2025-07-28 09:09:43.886 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单50平仓完成，平仓价格: 119337.03000000, 盈利: 49.73400000000, 原因: 止盈平仓
-2025-07-28 09:09:43.887 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始处理带单员订单结算，订单ID: 50, 带单员ID: 2, 盈利: 49.73400000000
-2025-07-28 09:09:43.887 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员订单结算数据格式化 - 订单ID: 50, 原始保证金: 4763.53, 格式化保证金: 4763.53, 原始持仓: 0.200, 格式化持仓: 0.200
-2025-07-28 09:09:43.887 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员订单盈利或持平，进入盈利结算分支 - 订单ID: 50, 盈利: 49.73400000000
-2025-07-28 09:09:43.887 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员盈利结算开始 - 带单员ID: 2, 保证金: 4763.53, 盈利: 49.73400000000
-2025-07-28 09:09:43.888 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 2, 金额: 4763.53, 类型: 保证金返还, 账户类型: 2
-2025-07-28 09:09:43.891 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 2, 金额: 4763.53, 类型: 保证金返还, 备注: 带单员订单50保证金返还
-2025-07-28 09:09:43.891 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 返还带单员保证金成功，带单员ID: 2, 金额: 4763.53, 订单ID: 50
-2025-07-28 09:09:43.892 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员手续费计算 - 成交数量: 0.200, 开仓价格: 119088.36000000, 平仓价格: 119337.03000000, 手续费率: 0.04%, 开仓手续费: 9.5271, 平仓手续费: 9.5470, 总手续费: 19.0741, 总盈利: 49.73400000000, 净利润: 30.65990000000
-2025-07-28 09:09:43.894 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 2, 金额: -9.5470, 类型: 手续费扣除, 账户类型: 2
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 2, 金额: -9.5470, 类型: 手续费扣除, 备注: 订单50手续费
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 扣除手续费成功，用户ID: 2, 金额: 9.5470, 订单ID: 50
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 带单员原始盈利为正，开始分配收益
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 使用原始利润按50%分配：50%给利润账户，50%给储备金
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员收益分配 - 原始利润: 49.73400000000, 平台费率: 50.00%, 储备金: 24.8670, 利润账户: 24.8670
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始增加带单员利润账户 ===
-2025-07-28 09:09:43.896 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员ID: 2, 利润金额: 24.8670, 订单ID: 50
-2025-07-28 09:09:43.897 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 带单员利润账户更新成功，影响行数: 1
-2025-07-28 09:09:43.897 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录带单员利润明细 - 带单员ID: 2, 金额: 24.8670, 备注: 订单50带单员利润收益
-2025-07-28 09:09:43.897 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始记录收益明细 ===
-2025-07-28 09:09:43.897 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 2, 金额: 24.8670, 类型: 2 (2=收益), 备注: 订单50带单员利润收益
-2025-07-28 09:09:43.897 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 获取用户邮箱: <EMAIL>
-2025-07-28 09:09:43.898 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库插入 - commission_record 表
-2025-07-28 09:09:43.900 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库插入结果 - 影响行数: 1
-2025-07-28 09:09:43.900 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 记录收益明细成功 - 用户ID: 2, 金额: 24.8670, 类型: 2, 备注: 订单50带单员利润收益
-2025-07-28 09:09:43.900 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 明细记录ID: 64
-2025-07-28 09:09:43.900 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 增加带单员利润账户成功 - 带单员ID: 2, 金额: 24.8670, 订单ID: 50
-2025-07-28 09:09:43.901 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 增加带单员储备金成功，带单员ID: 2, 金额: 24.8670, 订单ID: 50
-2025-07-28 09:09:43.901 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员盈利结算完成 - 带单员ID: 2, 保证金返还: 4763.53, 利润账户: 24.8670, 储备金: 24.8670
-2025-07-28 09:09:43.901 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始处理佣金分配，用户ID: 2, 总佣金: 9.5470, 订单ID: 50
-2025-07-28 09:09:43.902 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人: ID=1, 佣金比例=5.00, 一键跟单=0
-2025-07-28 09:09:43.902 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人有佣金比例但无一键跟单，累积佣金比例: 5.00
-2025-07-28 09:09:43.902 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 佣金分配完成，最终累积佣金比例: 5.00
-2025-07-28 09:09:43.902 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员订单盈利，批量设置相关订单返佣状态为已返 - 订单ID: 50, 盈利: 49.73400000000
-2025-07-28 09:09:43.904 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 批量更新相关订单返佣状态完成，带单员订单ID: 50, 盈利: 49.73400000000, 返佣状态: 已返, 相关单数: 3, 影响订单数: 3
-2025-07-28 09:09:43.904 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员订单结算完成，跟单关系清除将在所有相关订单结算完成后统一处理，带单员ID: 2
-2025-07-28 09:09:43.904 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 准备更新订单结算状态，订单ID: 50
-2025-07-28 09:09:43.904 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始更新订单结算状态 ===
-2025-07-28 09:09:43.904 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 50, 目标结算状态: 2
-2025-07-28 09:09:43.905 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新前状态 - 订单ID: 50, 当前结算状态: 1, 订单状态: 2
-2025-07-28 09:09:43.906 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库更新操作结果 - 影响行数: 1
-2025-07-28 09:09:43.907 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 更新订单结算状态成功 - 订单ID: 50, 结算状态: 1 → 2
-2025-07-28 09:09:43.907 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 🎯 结算状态更新验证成功 - 订单ID: 50, 状态: 2
-2025-07-28 09:09:43.907 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 订单结算状态更新完成 ===
-2025-07-28 09:09:43.907 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 带单员订单结算完成，订单ID: 50
-2025-07-28 09:09:43.908 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 更新带单员订单返利状态成功，订单ID: 50, 盈利: 49.73400000000, 返利状态: 1→2
-2025-07-28 09:09:43.909 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 找到2个跟单订单需要处理
-2025-07-28 09:09:43.909 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : === 第一步：为所有跟单订单保存平仓价格 ===
-2025-07-28 09:09:43.909 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 为第1个跟单订单保存平仓价格，订单ID: 51, 用户ID: 5
-2025-07-28 09:09:43.909 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试获取BTCUSDT的实时价格，Redis键: binance:ticker:BTCUSDT
-2025-07-28 09:09:43.910 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试成功，获取BTCUSDT实时价格: 119337.03000000
-2025-07-28 09:09:43.911 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 跟单订单平仓价格保存成功，订单ID: 51, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.911 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 为第2个跟单订单保存平仓价格，订单ID: 52, 用户ID: 7
-2025-07-28 09:09:43.911 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试获取BTCUSDT的实时价格，Redis键: binance:ticker:BTCUSDT
-2025-07-28 09:09:43.911 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 第1次尝试成功，获取BTCUSDT实时价格: 119337.03000000
-2025-07-28 09:09:43.912 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 跟单订单平仓价格保存成功，订单ID: 52, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.912 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : === 第二步：计算所有跟单订单的收益并结算 ===
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 计算跟单订单收益，订单ID: 51, 用户ID: 5, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : === 开始计算订单收益（使用保存的平仓价格） ===
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单ID: 51, 用户ID: 5, 交易对: BTCUSDT
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单方向: 1 (1=买涨, 2=买跌)
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 开仓价格: 119088.36000000, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 持仓数量: 0.040
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 买涨计算: (119337.03000000 - 119088.36000000) × 0.040 = 9.94680000000
-2025-07-28 09:09:43.914 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单盈利: 9.94680000000, 盈利状态: 1(盈利)
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单51收益计算完成，盈利: 9.94680000000, 盈利状态: 1
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始跟单订单结算 ===
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 51, 用户ID: 5, 带单员ID: 2
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单盈利: 9.94680000000, 盈利状态: 1
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单状态: 2, 结算状态: 1
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单订单结算数据格式化 - 订单ID: 51, 原始保证金: 952.70, 格式化保证金: 952.70, 原始持仓: 0.040, 格式化持仓: 0.040
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 判断盈亏情况 ===
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 51, 盈利金额: 9.94680000000
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 盈利是否大于0: true
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 进入盈利结算分支 - 订单ID: 51, 盈利: 9.94680000000
-2025-07-28 09:09:43.915 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 跟单员盈利结算开始 ===
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 51, 用户ID: 5, 带单员ID: 2
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利: 9.94680000000, 保证金: 952.70
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 手续费率: 0.04%, 平台费率: 50.00%
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始返还保证金 - 用户ID: 5, 金额: 952.70, 订单ID: 51
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 保证金返还数据库更新结果 - 用户ID: 5, 影响行数: 1
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录保证金返还明细 - 用户ID: 5, 金额: 952.70, 备注: 订单51保证金返还
-2025-07-28 09:09:43.916 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 5, 金额: 952.70, 类型: 保证金返还, 账户类型: 2
-2025-07-28 09:09:43.917 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 5, 金额: 952.70, 类型: 保证金返还, 备注: 订单51保证金返还
-2025-07-28 09:09:43.917 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 返还保证金成功 - 用户ID: 5, 金额: 952.70, 订单ID: 51
-2025-07-28 09:09:43.917 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 手续费计算 - 成交数量: 0.040, 开仓价格: 119088.36000000, 平仓价格: 119337.03000000, 手续费率: 0.04%, 开仓手续费: 1.9054, 平仓手续费: 1.9094, 总手续费: 3.8148, 总盈利: 9.94680000000, 净盈利: 6.13200000000
-2025-07-28 09:09:43.918 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 5, 金额: -1.9094, 类型: 手续费扣除, 账户类型: 2
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 5, 金额: -1.9094, 类型: 手续费扣除, 备注: 订单51手续费
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 扣除手续费成功，用户ID: 5, 金额: 1.9094, 订单ID: 51
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 盈利计算结果 ===
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利: 9.94680000000, 总手续费: 3.8148, 净利润: 6.13200000000
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利是否大于0: true
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 净利润是否大于等于0: true
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 原始盈利为正，开始分配收益
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 净利润大于等于0，按储备金比例分配收益
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单员收益分配 - 净利润: 6.13200000000, 平台费率: 50.00%, 带单员储备: 3.0660, 用户收益: 3.06600000000
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始增加用户收益账户 ===
-2025-07-28 09:09:43.919 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 5, 收益金额: 3.06600000000, 订单ID: 51
-2025-07-28 09:09:43.920 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户存在，当前收益余额: 6.06620
-2025-07-28 09:09:43.920 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库更新 - UPDATE front_user SET profit_balance = profit_balance + 3.06600000000 WHERE id = 5
-2025-07-28 09:09:43.921 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库更新结果 - 用户ID: 5, 影响行数: 1
-2025-07-28 09:09:43.921 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 数据库更新成功，影响行数: 1
-2025-07-28 09:09:43.922 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新后收益余额: 9.13220
-2025-07-28 09:09:43.922 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录收益明细 - 用户ID: 5, 金额: 3.06600000000, 备注: 订单51跟单收益
-2025-07-28 09:09:43.922 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始记录收益明细 ===
-2025-07-28 09:09:43.922 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 5, 金额: 3.06600000000, 类型: 2 (2=收益), 备注: 订单51跟单收益
-2025-07-28 09:09:43.922 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 获取用户邮箱: <EMAIL>
-2025-07-28 09:09:43.923 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库插入 - commission_record 表
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库插入结果 - 影响行数: 1
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 记录收益明细成功 - 用户ID: 5, 金额: 3.06600000000, 类型: 2, 备注: 订单51跟单收益
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 明细记录ID: 65
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 增加用户收益成功 - 用户ID: 5, 金额: 3.06600000000, 订单ID: 51
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 增加带单员储备金成功，带单员ID: 2, 金额: 3.0660, 订单ID: 51
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单员盈利结算完成 - 用户ID: 5, 保证金返还: 952.70, 用户收益: 3.06600000000, 带单员储备: 3.0660
-2025-07-28 09:09:43.924 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始处理佣金分配，用户ID: 5, 总佣金: 1.9094, 订单ID: 51
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人: ID=1, 佣金比例=5.00, 一键跟单=0
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人有佣金比例但无一键跟单，累积佣金比例: 5.00
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 佣金分配完成，最终累积佣金比例: 5.00
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 盈利结算完成，订单ID: 51, 总盈利: 9.94680000000, 平仓手续费: 1.9094, 净盈利: 6.13200000000
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始更新订单结算状态 ===
-2025-07-28 09:09:43.925 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 51, 目标结算状态: 2
-2025-07-28 09:09:43.926 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新前状态 - 订单ID: 51, 当前结算状态: 1, 订单状态: 2
-2025-07-28 09:09:43.926 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库更新操作结果 - 影响行数: 1
-2025-07-28 09:09:43.927 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 更新订单结算状态成功 - 订单ID: 51, 结算状态: 1 → 2
-2025-07-28 09:09:43.927 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 🎯 结算状态更新验证成功 - 订单ID: 51, 状态: 2
-2025-07-28 09:09:43.927 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 订单结算状态更新完成 ===
-2025-07-28 09:09:43.927 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单盈利，设置返佣状态为已返 - 订单ID: 51, 盈利: 9.94680000000
-2025-07-28 09:09:43.927 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新订单返佣状态成功，订单ID: 51, 盈利: 9.94680000000, 返佣状态: 已返
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单订单结算完成，订单ID: 51
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 跟单订单收益计算和结算成功，订单ID: 51, 用户ID: 5, 盈利: 9.94680000000
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 计算跟单订单收益，订单ID: 52, 用户ID: 7, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : === 开始计算订单收益（使用保存的平仓价格） ===
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单ID: 52, 用户ID: 7, 交易对: BTCUSDT
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单方向: 1 (1=买涨, 2=买跌)
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 开仓价格: 119088.36000000, 平仓价格: 119337.03000000
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 持仓数量: 0.030
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 买涨计算: (119337.03000000 - 119088.36000000) × 0.030 = 7.46010000000
-2025-07-28 09:09:43.928 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单盈利: 7.46010000000, 盈利状态: 1(盈利)
-2025-07-28 09:09:43.929 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 订单52收益计算完成，盈利: 7.46010000000, 盈利状态: 1
-2025-07-28 09:09:43.929 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始跟单订单结算 ===
-2025-07-28 09:09:43.929 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 52, 用户ID: 7, 带单员ID: 2
-2025-07-28 09:09:43.929 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单盈利: 7.46010000000, 盈利状态: 1
-2025-07-28 09:09:43.929 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单状态: 2, 结算状态: 1
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单订单结算数据格式化 - 订单ID: 52, 原始保证金: 714.53, 格式化保证金: 714.53, 原始持仓: 0.030, 格式化持仓: 0.030
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 判断盈亏情况 ===
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 52, 盈利金额: 7.46010000000
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 盈利是否大于0: true
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 进入盈利结算分支 - 订单ID: 52, 盈利: 7.46010000000
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 跟单员盈利结算开始 ===
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 52, 用户ID: 7, 带单员ID: 2
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利: 7.46010000000, 保证金: 714.53
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 手续费率: 0.04%, 平台费率: 50.00%
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始返还保证金 - 用户ID: 7, 金额: 714.53, 订单ID: 52
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 保证金返还数据库更新结果 - 用户ID: 7, 影响行数: 1
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录保证金返还明细 - 用户ID: 7, 金额: 714.53, 备注: 订单52保证金返还
-2025-07-28 09:09:43.930 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 7, 金额: 714.53, 类型: 保证金返还, 账户类型: 2
-2025-07-28 09:09:43.931 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 7, 金额: 714.53, 类型: 保证金返还, 备注: 订单52保证金返还
-2025-07-28 09:09:43.931 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 返还保证金成功 - 用户ID: 7, 金额: 714.53, 订单ID: 52
-2025-07-28 09:09:43.931 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 手续费计算 - 成交数量: 0.030, 开仓价格: 119088.36000000, 平仓价格: 119337.03000000, 手续费率: 0.04%, 开仓手续费: 1.4291, 平仓手续费: 1.4320, 总手续费: 2.8611, 总盈利: 7.46010000000, 净盈利: 4.59900000000
-2025-07-28 09:09:43.932 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录交易明细 - 用户ID: 7, 金额: -1.4320, 类型: 手续费扣除, 账户类型: 2
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 记录交易明细成功 - 用户ID: 7, 金额: -1.4320, 类型: 手续费扣除, 备注: 订单52手续费
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 扣除手续费成功，用户ID: 7, 金额: 1.4320, 订单ID: 52
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 盈利计算结果 ===
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利: 7.46010000000, 总手续费: 2.8611, 净利润: 4.59900000000
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 原始盈利是否大于0: true
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 净利润是否大于等于0: true
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 原始盈利为正，开始分配收益
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 净利润大于等于0，按储备金比例分配收益
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单员收益分配 - 净利润: 4.59900000000, 平台费率: 50.00%, 带单员储备: 2.2995, 用户收益: 2.29950000000
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始增加用户收益账户 ===
-2025-07-28 09:09:43.933 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 7, 收益金额: 2.29950000000, 订单ID: 52
-2025-07-28 09:09:43.934 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户存在，当前收益余额: 4.54940
-2025-07-28 09:09:43.934 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库更新 - UPDATE front_user SET profit_balance = profit_balance + 2.29950000000 WHERE id = 7
-2025-07-28 09:09:43.934 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库更新结果 - 用户ID: 7, 影响行数: 1
-2025-07-28 09:09:43.934 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 数据库更新成功，影响行数: 1
-2025-07-28 09:09:43.935 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新后收益余额: 6.84890
-2025-07-28 09:09:43.935 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始记录收益明细 - 用户ID: 7, 金额: 2.29950000000, 备注: 订单52跟单收益
-2025-07-28 09:09:43.935 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始记录收益明细 ===
-2025-07-28 09:09:43.935 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 7, 金额: 2.29950000000, 类型: 2 (2=收益), 备注: 订单52跟单收益
-2025-07-28 09:09:43.936 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 获取用户邮箱: <EMAIL>
-2025-07-28 09:09:43.936 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库插入 - commission_record 表
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库插入结果 - 影响行数: 1
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 记录收益明细成功 - 用户ID: 7, 金额: 2.29950000000, 类型: 2, 备注: 订单52跟单收益
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 明细记录ID: 66
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 增加用户收益成功 - 用户ID: 7, 金额: 2.29950000000, 订单ID: 52
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 增加带单员储备金成功，带单员ID: 2, 金额: 2.2995, 订单ID: 52
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单员盈利结算完成 - 用户ID: 7, 保证金返还: 714.53, 用户收益: 2.29950000000, 带单员储备: 2.2995
-2025-07-28 09:09:43.937 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 开始处理佣金分配，用户ID: 7, 总佣金: 1.4320, 订单ID: 52
-2025-07-28 09:09:43.938 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人: ID=6, 佣金比例=20.00, 一键跟单=0
-2025-07-28 09:09:43.938 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第1层推荐人有佣金比例但无一键跟单，累积佣金比例: 20.00
-2025-07-28 09:09:43.939 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第2层推荐人: ID=5, 佣金比例=15.00, 一键跟单=1
-2025-07-28 09:09:43.939 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始记录收益明细 ===
-2025-07-28 09:09:43.939 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 用户ID: 5, 金额: 0.5012, 类型: 1 (2=收益), 备注: 订单52第2层推荐佣金
-2025-07-28 09:09:43.939 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 获取用户邮箱: <EMAIL>
-2025-07-28 09:09:43.940 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 执行数据库插入 - commission_record 表
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库插入结果 - 影响行数: 1
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 记录收益明细成功 - 用户ID: 5, 金额: 0.5012, 类型: 1, 备注: 订单52第2层推荐佣金
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 明细记录ID: 67
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 成功发放佣金给用户5, 金额: 0.5012, 层级: 2, 订单ID: 52
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第2层推荐人有一键跟单，发放佣金后累积归零
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第3层推荐人: ID=1, 佣金比例=5.00, 一键跟单=0
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 第3层推荐人有佣金比例但无一键跟单，累积佣金比例: 5.00
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 佣金分配完成，最终累积佣金比例: 5.00
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 盈利结算完成，订单ID: 52, 总盈利: 7.46010000000, 平仓手续费: 1.4320, 净盈利: 4.59900000000
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 开始更新订单结算状态 ===
-2025-07-28 09:09:43.941 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单ID: 52, 目标结算状态: 2
-2025-07-28 09:09:43.942 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新前状态 - 订单ID: 52, 当前结算状态: 1, 订单状态: 2
-2025-07-28 09:09:43.942 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 数据库更新操作结果 - 影响行数: 1
-2025-07-28 09:09:43.943 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : ✅ 更新订单结算状态成功 - 订单ID: 52, 结算状态: 1 → 2
-2025-07-28 09:09:43.943 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 🎯 结算状态更新验证成功 - 订单ID: 52, 状态: 2
-2025-07-28 09:09:43.943 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : === 订单结算状态更新完成 ===
-2025-07-28 09:09:43.943 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 订单盈利，设置返佣状态为已返 - 订单ID: 52, 盈利: 7.46010000000
-2025-07-28 09:09:43.944 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 更新订单返佣状态成功，订单ID: 52, 盈利: 7.46010000000, 返佣状态: 已返
-2025-07-28 09:09:43.944 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.SettlementServiceImpl : 跟单订单结算完成，订单ID: 52
-2025-07-28 09:09:43.944 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 跟单订单收益计算和结算成功，订单ID: 52, 用户ID: 7, 盈利: 7.46010000000
-2025-07-28 09:09:43.944 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 跟单订单处理完成，总数: 2, 需处理: 2, 成功: 2
-2025-07-28 09:09:43.944 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 开始更新带单员2的盈利订单返利状态
-2025-07-28 09:09:43.945 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 查询到0个符合条件的盈利订单
-2025-07-28 09:09:43.945 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 返利状态更新完成，带单员ID: 2, 更新盈利订单数量: 0
-2025-07-28 09:09:43.945 - INFO 11588 --- [pool-3-thread-2] c.f.s.i.FollowRelationCleanupServiceImpl : 开始清除带单员ID: 2 的无效跟单关系（结算后版本）
-2025-07-28 09:09:43.945 - INFO 11588 --- [pool-3-thread-2] c.f.s.i.FollowRelationCleanupServiceImpl : 找到 2 个跟单用户，开始验证余额条件
-2025-07-28 09:09:43.946 - INFO 11588 --- [pool-3-thread-2] c.f.s.i.FollowRelationCleanupServiceImpl : 带单员配置 - 最低跟单金额: 1000.00000000, 最高跟单金额: 2000.00000000
-2025-07-28 09:09:43.946 - INFO 11588 --- [pool-3-thread-2] c.f.s.i.FollowRelationCleanupServiceImpl : 带单员ID: 2 的跟单关系清除完成（结算后版本），共清除 0 个关系
-2025-07-28 09:09:43.946 - INFO 11588 --- [pool-3-thread-2] c.f.s.impl.DeliveryOrderServiceImpl : 平仓处理完成，订单ID: 50, 原因: 止盈平仓
-2025-07-28 09:09:44.103 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.AutoCloseServiceImpl : 订单50平仓完成
-2025-07-28 09:09:44.104 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.AutoCloseServiceImpl : 自动平仓成功，订单ID: 50, 原因: 止盈平仓
-2025-07-28 09:09:44.105 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.AutoCloseServiceImpl : 带单员订单50没有跟单订单需要处理
-2025-07-28 09:09:44.105 - INFO 11588 --- [pool-3-thread-2] c.f.service.impl.AutoCloseServiceImpl : 自动平仓完成，跟单关系清除将在结算完成后执行，带单员ID: 2
