package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.Agreement;
import com.frontapi.mapper.AgreementMapper;
import com.frontapi.service.AgreementService;
import org.springframework.stereotype.Service;

@Service
public class AgreementServiceImpl extends ServiceImpl<AgreementMapper, Agreement> implements AgreementService {
    
    @Override
    public Agreement getAgreementDetail(Long id) {
        LambdaQueryWrapper<Agreement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Agreement::getId, id)
               .eq(Agreement::getStatus, 1);  // 只查询启用的条款
        
        Agreement agreement = this.getOne(wrapper);
        
        if (agreement != null && agreement.getContent() != null) {
            // 处理换行符，确保前端显示正确
            agreement.setContent(agreement.getContent()
                    .replaceAll("\\r\\n", "\n")
                    .replaceAll("\\r", "\n"));
        }
        
        return agreement;
    }
} 