# 自动平仓功能完整实现说明

## 🎯 核心目标

实现基于止盈止损的自动平仓功能，确保**不重复执行**，解决手动平仓与自动平仓的并发冲突问题。

## 📊 数据库状态设计

### 订单状态字段 `status`
```sql
`status` tinyint(4) DEFAULT 1 COMMENT '状态(0:开仓处理中,1:持仓中,2:已平仓,3:平仓处理中)'
```

### 状态含义
- **status = 0**：开仓处理中（开仓分润正在执行）
- **status = 1**：持仓中（开仓完成，可以平仓）
- **status = 2**：已平仓（平仓完成）
- **status = 3**：平仓处理中（平仓逻辑正在执行）

## 🔐 Redis 锁机制

### 锁的类型和用途
1. **开仓锁**：`opening:{orderId}` - 保护开仓分润逻辑（60秒超时）
2. **平仓锁**：`closing:{orderId}` - 保护平仓操作（30秒超时）

### 防重复执行原理
- **Redis 标记**：原子性设置处理标记
- **状态更新**：基于当前状态的原子更新
- **双重保护**：Redis + 数据库状态的双重机制

## 🔄 完整流程实现

### 1. 订单创建流程
```
用户下单
    ↓
创建订单：status = 0 (开仓处理中)
    ↓
设置 Redis 标记：opening:{orderId}
    ↓
异步执行开仓分润
    ├─ 成功：status = 1 (持仓中)
    └─ 失败：处理异常
    ↓
清除 Redis 标记
```

### 2. 自动平仓流程
```
定时监控（每5秒）
    ↓
查询：status = 1 AND (take_profit > 0 OR stop_loss > 0)
    ↓
检查价格触发条件
    ↓
设置 Redis 标记：closing:{orderId}
    ↓
原子更新：status 1 → 3
    ↓
执行平仓逻辑
    ↓
更新最终状态：status = 2
    ↓
清除 Redis 标记
```

### 3. 手动平仓流程
```
用户点击平仓
    ↓
检查订单状态
    ├─ status = 0：提示"开仓处理中"
    ├─ status = 2：提示"已平仓"
    ├─ status = 3：提示"平仓处理中"
    └─ status = 1：继续
    ↓
设置 Redis 标记：closing:{orderId}
    ↓
原子更新：status 1 → 3
    ↓
执行平仓逻辑
    ↓
更新最终状态：status = 2
    ↓
清除 Redis 标记
```

## 🛡️ 冲突场景处理

### 场景1：开仓分润 vs 平仓操作
- **保护机制**：平仓只处理 `status = 1` 的订单
- **结果**：开仓未完成时不会被平仓

### 场景2：手动平仓 vs 自动平仓
- **保护机制**：Redis锁 + 原子状态更新
- **结果**：只有一个操作能成功

### 场景3：重复手动平仓
- **保护机制**：状态检查 + Redis锁 + 原子更新
- **结果**：多层防护确保不重复

### 场景4：平仓过程中的干扰
- **保护机制**：`status = 3` 期间拒绝新请求
- **结果**：平仓过程不会被打断

## 🔧 关键实现代码

### 1. 数据库状态更新方法
```java
// 开仓完成
@Update("UPDATE delivery_order SET status = 1 WHERE id = #{orderId} AND status = 0")
int updateStatusToOpen(@Param("orderId") Long orderId);

// 开始平仓
@Update("UPDATE delivery_order SET status = 3 WHERE id = #{orderId} AND status = 1")
int updateStatusToClosing(@Param("orderId") Long orderId);

// 平仓完成
@Update("UPDATE delivery_order SET status = 2, close_time = NOW() WHERE id = #{orderId} AND status = 3")
int updateStatusToClosed(@Param("orderId") Long orderId);

// 平仓失败恢复
@Update("UPDATE delivery_order SET status = 1 WHERE id = #{orderId} AND status = 3")
int rollbackStatusToOpen(@Param("orderId") Long orderId);
```

### 2. 防重复执行核心方法
```java
private boolean tryStartCloseProcess(Long orderId) {
    String closingKey = "closing:" + orderId;
    
    // 1. 检查订单当前状态
    DeliveryOrder order = deliveryOrderMapper.selectById(orderId);
    if (order.getStatus() != 1) {
        return false;
    }
    
    // 2. 设置 Redis 标记
    Boolean setResult = stringRedisTemplate.opsForValue()
        .setIfAbsent(closingKey, "1", Duration.ofSeconds(30));
    if (!Boolean.TRUE.equals(setResult)) {
        return false;
    }
    
    // 3. 原子更新订单状态
    int affectedRows = deliveryOrderMapper.updateStatusToClosing(orderId);
    if (affectedRows != 1) {
        stringRedisTemplate.delete(closingKey);
        return false;
    }
    
    return true;
}
```

### 3. 自动平仓监控查询
```sql
SELECT * FROM delivery_order 
WHERE status = 1 
  AND (take_profit > 0 OR stop_loss > 0)
```

## ⚙️ 配置参数

```yaml
# application.yml
auto-close:
  enabled: true              # 启用自动平仓功能
  startup-delay: 10          # 启动延迟10秒
  check-interval: 5          # 每5秒检查一次
  thread-pool-size: 2        # 使用2个线程处理
```

## 🚀 启动和运行

### 自动启动
```java
@Configuration
public class AutoCloseConfig implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {
        if (autoCloseEnabled) {
            Thread.sleep(startupDelay * 1000L);
            autoCloseService.startAutoCloseMonitoring();
        }
    }
}
```

### 优雅关闭
```java
@PreDestroy
public void shutdown() {
    autoCloseService.stopAutoCloseMonitoring();
}
```

## 📈 技术优势

1. **完全防重复**：Redis + 数据库双重保护
2. **高性能**：异步处理，精确查询
3. **高可靠性**：异常隔离，自动恢复
4. **易维护**：清晰的状态流转，详细日志
5. **零配置**：应用启动自动运行

## 🎯 使用方式

### 创建订单时设置止盈止损
```java
DeliveryOrder order = new DeliveryOrder();
order.setTakeProfit(new BigDecimal("45000.00")); // 止盈价格
order.setStopLoss(new BigDecimal("41000.00"));   // 止损价格
// 系统会自动监控并在合适时机平仓
```

### 用户体验
- **开仓处理中**：提示"订单开仓处理中，请稍后再试"
- **平仓处理中**：提示"订单平仓处理中，请稍后再试"
- **已平仓**：提示"订单已平仓"
- **正常平仓**：执行平仓操作

## 🔍 监控和日志

### 关键日志
```
订单123开仓分润完成，状态更新为持仓中
触发自动平仓条件，订单ID: 123, 当前价格: 45000.00, 原因: 止盈平仓
订单123平仓完成
带单员订单123平仓完成，开始处理 2 个跟单订单
```

### 状态统计
- 可以统计各状态订单数量
- 监控平仓成功率
- 分析冲突频率

## ✅ 测试验证

1. **功能测试**：止盈止损条件判断
2. **并发测试**：手动vs自动平仓冲突
3. **异常测试**：网络异常、系统重启
4. **性能测试**：大量订单监控性能

这个实现确保了自动平仓功能的**完全防重复执行**，通过状态机制和Redis锁的双重保护，彻底解决了各种并发冲突问题！
