# 下单到平仓完整计算公式

## 📊 **基础参数说明**

### 订单基础数据
- **成交数量** (positionAmount)：持仓数量，格式化为3位小数
- **开仓价格** (openPrice)：订单开仓时的价格
- **平仓价格** (closePrice)：订单平仓时的价格
- **保证金** (marginAmount)：用户投入的保证金，格式化为2位小数
- **方向** (direction)：1=买涨，2=买跌

### 系统参数
- **手续费率** (copyTradeFee)：默认0.04%
- **平台费率** (platformFeeRate)：默认50%
- **佣金比例** (commissionRate)：推荐人的佣金比例，各层级不同

## 🔢 **核心计算公式**

### 1. 利润计算

#### 买涨订单 (direction = 1)
```
利润 = (平仓价格 - 开仓价格) × 成交数量
```

#### 买跌订单 (direction = 2)
```
利润 = (开仓价格 - 平仓价格) × 成交数量
```

**示例**：
```
成交数量：0.100 BTC
开仓价格：118,118 USDT
平仓价格：118,042.01 USDT
方向：买涨

利润 = (118,042.01 - 118,118) × 0.100 = -7.59 USDT（亏损）
```

### 2. 手续费计算

#### 开仓手续费
```
开仓手续费 = 成交数量 × 开仓价格 × 手续费率 ÷ 100
```

#### 平仓手续费
```
平仓手续费 = 成交数量 × 平仓价格 × 手续费率 ÷ 100
```

#### 总手续费
```
总手续费 = 开仓手续费 + 平仓手续费
```

**示例**：
```
成交数量：0.100 BTC
开仓价格：118,118 USDT
平仓价格：118,042.01 USDT
手续费率：0.04%

开仓手续费 = 0.100 × 118,118 × 0.04% = 4.7247 USDT
平仓手续费 = 0.100 × 118,042.01 × 0.04% = 4.7217 USDT
总手续费 = 4.7247 + 4.7217 = 9.4464 USDT
```

### 3. 净盈利计算

```
净盈利 = 利润 - 平仓手续费
```

**注意**：只扣除平仓手续费，开仓手续费在开仓时已扣除。

### 4. 收益分配计算

#### 4.1 跟单员收益分配

##### 情况1：盈利小于总手续费
```
if (利润 < 总手续费) {
    用户收益 = 净盈利
    带单员储备 = 0
}
```

##### 情况2：盈利大于等于总手续费
```
if (利润 >= 总手续费) {
    带单员储备 = 净盈利 × 平台费率 ÷ 100
    用户收益 = 净盈利 - 带单员储备
}
```

#### 4.2 带单员收益分配

##### 情况1：盈利小于总手续费
```
if (利润 < 总手续费) {
    带单员利润账户 = 净盈利
    带单员储备金 = 0
}
```

##### 情况2：盈利大于等于总手续费
```
if (利润 >= 总手续费) {
    带单员利润账户 = 净盈利 × 50%
    带单员储备金 = 净盈利 × 50%
}
```

### 5. 佣金分配计算

#### 5.1 开仓佣金（订单创建时）
```
佣金基数 = 开仓手续费
佣金金额 = 开仓手续费 × 累积佣金比例 ÷ 100
```

#### 5.2 平仓佣金（订单平仓时）
```
佣金基数 = 平仓手续费
佣金金额 = 平仓手续费 × 累积佣金比例 ÷ 100
```

#### 5.3 佣金累积规则
1. 从下单用户开始，向上查找推荐链（最多10层）
2. 每层推荐人的佣金比例累积
3. 遇到有一键跟单的推荐人时：
   - 发放累积的佣金
   - 累积比例归零
   - 继续向上查找

### 6. 保证金处理

#### 盈利情况
```
返还保证金 = 原保证金金额（全额返还）
```

#### 亏损情况
```
if (亏损金额 >= 保证金) {
    返还保证金 = 0（保证金全部扣除）
} else {
    返还保证金 = 保证金 - 亏损金额
}
```

## 💰 **完整计算示例**

### 示例1：跟单员盈利订单

**基础数据**：
- 成交数量：0.100 BTC
- 开仓价格：118,000 USDT
- 平仓价格：119,000 USDT
- 保证金：2,000 USDT
- 方向：买涨
- 手续费率：0.04%
- 平台费率：50%

**计算过程**：

1. **利润计算**：
   ```
   利润 = (119,000 - 118,000) × 0.100 = 100 USDT
   ```

2. **手续费计算**：
   ```
   开仓手续费 = 0.100 × 118,000 × 0.04% = 4.72 USDT
   平仓手续费 = 0.100 × 119,000 × 0.04% = 4.76 USDT
   总手续费 = 4.72 + 4.76 = 9.48 USDT
   ```

3. **净盈利计算**：
   ```
   净盈利 = 100 - 4.76 = 95.24 USDT
   ```

4. **收益分配**（盈利 > 总手续费）：
   ```
   带单员储备 = 95.24 × 50% = 47.62 USDT
   用户收益 = 95.24 - 47.62 = 47.62 USDT
   ```

5. **保证金返还**：
   ```
   返还保证金 = 2,000 USDT（全额返还）
   ```

6. **佣金分配**：
   ```
   开仓佣金基数 = 4.72 USDT
   平仓佣金基数 = 4.76 USDT
   ```

### 示例2：跟单员亏损订单

**基础数据**：
- 成交数量：0.100 BTC
- 开仓价格：118,000 USDT
- 平仓价格：117,000 USDT
- 保证金：2,000 USDT
- 方向：买涨
- 手续费率：0.04%

**计算过程**：

1. **利润计算**：
   ```
   利润 = (117,000 - 118,000) × 0.100 = -100 USDT（亏损）
   ```

2. **手续费计算**：
   ```
   平仓手续费 = 0.100 × 117,000 × 0.04% = 4.68 USDT
   ```

3. **保证金处理**：
   ```
   亏损金额 = 100 USDT
   返还保证金 = 2,000 - 100 = 1,900 USDT
   ```

4. **手续费扣除**：
   ```
   从跟单账户扣除：4.68 USDT
   ```

## 📋 **资金流向总结**

### 开仓时
1. **扣除保证金**：从跟单账户转入系统
2. **扣除开仓手续费**：从跟单账户扣除
3. **开仓佣金分配**：给推荐人发放佣金

### 平仓时
1. **返还保证金**：根据盈亏情况返还
2. **扣除平仓手续费**：从跟单账户扣除
3. **收益分配**：盈利分配给用户和带单员
4. **平仓佣金分配**：给推荐人发放佣金

### 账户类型
- **跟单账户**：用于交易的主要账户
- **利润账户**：存放交易盈利
- **佣金账户**：存放推荐佣金
- **储备金账户**：带单员的储备资金

这套计算公式确保了交易过程中的资金安全、收益分配公平，以及佣金体系的完整性。
