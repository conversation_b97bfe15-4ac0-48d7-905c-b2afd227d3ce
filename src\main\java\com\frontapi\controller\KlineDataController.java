package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.entity.ExchangePairInfo;
import com.frontapi.service.ExchangePairInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.data.redis.core.StringRedisTemplate;

@Slf4j
@RestController
@RequestMapping("/api/market/kline")
public class KlineDataController {
    private final List<SseEmitter> emitters = new CopyOnWriteArrayList<>();
    private final Map<String, SseEmitter> symbolEmitters = new ConcurrentHashMap<>();
    // 新增：保存每个emitter的symbol和interval
    private final Map<String, String[]> emitterMeta = new ConcurrentHashMap<>();

    @Autowired
    private ExchangePairInfoService exchangePairInfoService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${binance.api-prefix}")
    private String binanceApiPrefix;

    /**
     * SSE实时数据推送 - 所有交易对
     */
    @GetMapping("/stream")
    public SseEmitter streamKlineData() {
        SseEmitter emitter = new SseEmitter(0L); // 永不超时
        emitters.add(emitter);
        emitter.onCompletion(() -> emitters.remove(emitter));
        emitter.onTimeout(() -> emitters.remove(emitter));
        return emitter;
    }

    /**
     * SSE实时数据推送 - 指定交易对
     */
    @GetMapping("/stream/symbol")
    public SseEmitter streamSymbolKlineData(@RequestParam String symbol, @RequestParam(defaultValue = "1m") String interval) {
        SseEmitter emitter = new SseEmitter(0L);
        String uuid = java.util.UUID.randomUUID().toString();
        String key = symbol + "_" + interval + "_" + uuid;
        symbolEmitters.put(key, emitter);
        emitterMeta.put(key, new String[]{symbol, interval});
        emitter.onCompletion(() -> { symbolEmitters.remove(key); emitterMeta.remove(key); });
        emitter.onTimeout(() -> { symbolEmitters.remove(key); emitterMeta.remove(key); });
        return emitter;
    }

    /**
     * 获取指定交易对的实时ticker数据
     */
    @GetMapping("/ticker")
    public Map<String, Object> getTickerData(@RequestParam String symbol) {
        return getTickerDataFromBinance(symbol);
    }

    /**
     * 获取指定交易对的K线数据
     */
    @GetMapping("/kline")
    public Map<String, Object> getKlineData(@RequestParam String symbol, 
                                           @RequestParam(defaultValue = "1m") String interval,
                                           @RequestParam(defaultValue = "100") int limit) {
        List<Object[]> klineRaw = getKlineDataFromBinance(symbol, interval, limit);
        Map<String, Object> kline = formatKline(klineRaw != null ? klineRaw : new ArrayList<>());
        Map<String, Object> result = new HashMap<>();
        result.put("symbol", symbol);
        result.put("interval", interval);
        result.put("kline", kline);
        return result;
    }

    /**
     * 获取深度数据
     */
    @GetMapping("/depth")
    public Map<String, Object> getDepthData(@RequestParam String symbol, 
                                           @RequestParam(defaultValue = "20") int limit) {
        try {
            String url = String.format(binanceApiPrefix + "/v1/depth?symbol=%s&limit=%d", symbol, limit);
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            return response != null ? response : new HashMap<>();
        } catch (Exception e) {
            log.warn("获取{}深度数据失败: {}", symbol, e.getMessage());
            return new HashMap<>();
        }
    }

    // 新增：格式化K线数据为前端可用格式
    private Map<String, Object> formatKline(List<Object[]> rawKline) {
        List<String> categoryData = new ArrayList<>();
        List<List<Double>> values = new ArrayList<>();
        List<Map<String, Object>> volumes = new ArrayList<>();
        List<Double> closes = new ArrayList<>();
        List<Long> rawTimestamps = new ArrayList<>();
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm");
        for (Object[] k : rawKline) {
            long ts = ((Number)k[0]).longValue();
            String timeStr = sdf.format(new java.util.Date(ts));
            categoryData.add(timeStr);
            values.add(Arrays.asList(
                Double.valueOf(k[1].toString()), // open
                Double.valueOf(k[4].toString()), // close
                Double.valueOf(k[3].toString()), // low
                Double.valueOf(k[2].toString())  // high
            ));
            double volume = Double.valueOf(k[5].toString());
            double close = Double.valueOf(k[4].toString());
            double open = Double.valueOf(k[1].toString());
            Map<String, Object> volumeItem = new HashMap<>();
            volumeItem.put("value", volume);
            Map<String, Object> itemStyle = new HashMap<>();
            itemStyle.put("color", close > open ? "#00b26a" : "#e74c3c");
            volumeItem.put("itemStyle", itemStyle);
            volumes.add(volumeItem);
            closes.add(close);
            rawTimestamps.add(ts);
        }
        Map<String, Object> kline = new HashMap<>();
        kline.put("categoryData", categoryData);
        kline.put("values", values);
        kline.put("volumes", volumes);
        kline.put("closes", closes);
        kline.put("rawTimestamps", rawTimestamps);
        return kline;
    }

    // 定时任务，每5秒推送一次K线数据
    @Scheduled(fixedRate = 5000)
    public void pushKlineData() {
        List<ExchangePairInfo> enabledPairs = exchangePairInfoService.list(
                new QueryWrapper<ExchangePairInfo>()
                        .eq("is_enabled", 1)
                        .orderByAsc("sort")
        );
        List<Map<String, Object>> allPairsData = new ArrayList<>();
        for (ExchangePairInfo pair : enabledPairs) {
            String symbol = pair.getPairName();
            try {
                Map<String, Object> pairData = new HashMap<>();
                pairData.put("symbol", symbol);
                pairData.put("label", pair.getTokenName() + "/USDT");
                pairData.put("exchangeName", pair.getExchangeName());
                pairData.put("tokenName", pair.getTokenName());
                pairData.put("logoUrl", pair.getLogoUrl());
                Map<String, Object> tickerData = getTickerDataFromBinance(symbol);
                pairData.put("ticker", tickerData);
                // 只推送1m周期K线
                List<Object[]> klineRaw = getKlineDataFromBinance(symbol, "1m", 100);
                Map<String, Object> kline = formatKline(klineRaw != null ? klineRaw : new ArrayList<>());
                pairData.put("kline", kline);
                allPairsData.add(pairData);
            } catch (Exception e) {
                log.warn("获取交易对 {} 数据失败: {}", symbol, e.getMessage());
            }
        }
        ObjectMapper objectMapper = new ObjectMapper();
        for (SseEmitter emitter : emitters) {
            try {
                String sseData = objectMapper.writeValueAsString(allPairsData);
                emitter.send(sseData);
            } catch (Exception e) {
                emitters.remove(emitter);
                log.warn("推送K线数据失败: {}", e.getMessage());
            }
        }
    }

    // 定时任务，每3秒推送一次每个symbol的SSE数据
    @Scheduled(fixedRate = 3000)
    public void pushSymbolEmitters() {
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> removeKeys = new ArrayList<>();
        for (Map.Entry<String, SseEmitter> entry : symbolEmitters.entrySet()) {
            String key = entry.getKey();
            SseEmitter emitter = entry.getValue();
            String[] meta = emitterMeta.get(key);
            if (meta == null) {
                removeKeys.add(key);
                continue;
            }
            String symbol = meta[0];
            String interval = meta[1];

            // ticker
            Map<String, Object> ticker = getTickerDataFromBinance(symbol);
            ticker.put("type", "ticker");

            // kline
            List<Object[]> klineRaw = getKlineDataFromBinance(symbol, interval, 100);
            Map<String, Object> kline = formatKline(klineRaw != null ? klineRaw : new ArrayList<>());
            Map<String, Object> klineMsg = new HashMap<>();
            klineMsg.put("type", "kline");
            klineMsg.put("symbol", symbol);
            klineMsg.put("interval", interval);
            klineMsg.put("kline", kline);

            // depth
            Map<String, Object> depth = getDepthData(symbol, 20);
            depth.put("type", "depth");
            depth.put("symbol", symbol);

            try {
                emitter.send(objectMapper.writeValueAsString(ticker));
                emitter.send(objectMapper.writeValueAsString(klineMsg));
                emitter.send(objectMapper.writeValueAsString(depth));
            } catch (Exception e) {
                removeKeys.add(key);
            }
        }
        for (String key : removeKeys) {
            symbolEmitters.remove(key);
            emitterMeta.remove(key);
        }
    }

    /**
     * 获取24小时行情数据
     */
    private Map<String, Object> getTickerDataFromBinance(String symbol) {
        try {
            String redisKey = "binance:ticker:" + symbol;
            String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (tickerJson != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> response = objectMapper.readValue(tickerJson, Map.class);
                Map<String, Object> ticker = new HashMap<>();
                ticker.put("price", response.get("lastPrice"));
                ticker.put("changeRate", response.get("priceChangePercent"));
                ticker.put("high", response.get("highPrice"));
                ticker.put("low", response.get("lowPrice"));
                ticker.put("vol", response.get("volume"));
                ticker.put("amount", response.get("quoteVolume"));
                return ticker;
            }
        } catch (Exception e) {
            log.warn("获取{}行情数据失败: {}", symbol, e.getMessage());
        }
        return new HashMap<>();
    }
    
    /**
     * 获取K线数据
     * @param symbol 交易对
     * @param interval 时间间隔 (1m, 5m, 15m, 1h, 4h, 1d)
     * @param limit 数据条数
     * @return K线数据列表 [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量]
     */
    private List<Object[]> getKlineDataFromBinance(String symbol, String interval, int limit) {
        try {
            String url = String.format(binanceApiPrefix + "/v3/klines?symbol=%s&interval=%s&limit=%d", symbol, interval, limit);
            
            List<List<Object>> response = restTemplate.getForObject(url, List.class);
            
            if (response != null) {
                List<Object[]> klineData = new ArrayList<>();
                for (List<Object> candle : response) {
                    // Binance K线数据格式: [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, 成交额, 成交笔数, 主动买入成交量, 主动买入成交额, 忽略]
                    Object[] kline = new Object[6];
                    kline[0] = candle.get(0); // 开盘时间
                    kline[1] = candle.get(1); // 开盘价
                    kline[2] = candle.get(2); // 最高价
                    kline[3] = candle.get(3); // 最低价
                    kline[4] = candle.get(4); // 收盘价
                    kline[5] = candle.get(5); // 成交量
                    klineData.add(kline);
                }
                return klineData;
            }
        } catch (Exception e) {
            log.warn("获取{} {}K线数据失败: {}", symbol, interval, e.getMessage());
        }
        return null;
    }
    
    /**
     * 获取所有交易对数据（一次性接口）
     */
    @GetMapping("/all")
    public Map<String, Object> getAllKlineData() {
        List<ExchangePairInfo> enabledPairs = exchangePairInfoService.list(
                new QueryWrapper<ExchangePairInfo>()
                        .eq("is_enabled", 1)
                        .orderByAsc("sort")
        );
        List<Map<String, Object>> allPairsData = new ArrayList<>();
        for (ExchangePairInfo pair : enabledPairs) {
            String symbol = pair.getPairName();
            try {
                Map<String, Object> pairData = new HashMap<>();
                pairData.put("symbol", symbol);
                pairData.put("label", pair.getTokenName() + "/USDT");
                pairData.put("exchangeName", pair.getExchangeName());
                pairData.put("tokenName", pair.getTokenName());
                pairData.put("logoUrl", pair.getLogoUrl());
                Map<String, Object> tickerData = getTickerDataFromBinance(symbol);
                pairData.put("ticker", tickerData);
                // 只返回1m周期K线
                List<Object[]> klineRaw = getKlineDataFromBinance(symbol, "1m", 100);
                Map<String, Object> kline = formatKline(klineRaw != null ? klineRaw : new ArrayList<>());
                pairData.put("kline", kline);
                allPairsData.add(pairData);
            } catch (Exception e) {
                log.warn("获取交易对 {} 数据失败: {}", symbol, e.getMessage());
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("data", allPairsData);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
} 