# 返佣状态持平处理修改说明

## 🎯 **修改需求**

用户要求：在平仓最后的返佣状态处理中，增加持平的情况也设为已返，只有亏损不动是1。

**修改前的逻辑**:
- 盈利：`rebate_status = 2` (已返)
- 亏损：`rebate_status = 1` (未返)
- 持平：`rebate_status = 1` (未返) ❌

**修改后的逻辑**:
- 盈利：`rebate_status = 2` (已返)
- 持平：`rebate_status = 2` (已返) ✅
- 亏损：`rebate_status = 1` (未返)

## 🔧 **修改内容**

### 修改1：单个订单返佣状态更新
**文件**: `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`
**方法**: `updateRebateStatusByProfit`

**修改前**:
```java
if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0) {
    // 盈利：设置为已返
    rebateStatus = 2;
    statusDesc = "已返";
} else {
    // 亏损或持平：设置为未返
    rebateStatus = 1;
    statusDesc = "未返";
}
```

**修改后**:
```java
if (profit != null && profit.compareTo(BigDecimal.ZERO) >= 0) {
    // 盈利或持平：设置为已返
    rebateStatus = 2;
    statusDesc = "已返";
    if (profit.compareTo(BigDecimal.ZERO) > 0) {
        log.info("订单盈利，设置返佣状态为已返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
    } else {
        log.info("订单持平，设置返佣状态为已返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
    }
} else {
    // 亏损：设置为未返
    rebateStatus = 1;
    statusDesc = "未返";
    log.info("订单亏损，设置返佣状态为未返 - 订单ID: {}, 盈利: {}", order.getId(), profit);
}
```

### 修改2：批量订单返佣状态更新
**文件**: `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`
**方法**: `updateRelatedOrdersRebateStatus`

**修改前**:
```java
if (profit != null && profit.compareTo(BigDecimal.ZERO) > 0) {
    // 盈利：设置为已返
    rebateStatus = 2;
    statusDesc = "已返";
} else {
    // 亏损或持平：设置为未返
    rebateStatus = 1;
    statusDesc = "未返";
}
```

**修改后**:
```java
if (profit != null && profit.compareTo(BigDecimal.ZERO) >= 0) {
    // 盈利或持平：设置为已返
    rebateStatus = 2;
    statusDesc = "已返";
    if (profit.compareTo(BigDecimal.ZERO) > 0) {
        log.info("带单员订单盈利，批量设置相关订单返佣状态为已返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
    } else {
        log.info("带单员订单持平，批量设置相关订单返佣状态为已返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
    }
} else {
    // 亏损：设置为未返
    rebateStatus = 1;
    statusDesc = "未返";
    log.info("带单员订单亏损，批量设置相关订单返佣状态为未返 - 订单ID: {}, 盈利: {}", leaderOrder.getId(), profit);
}
```

### 修改3：带单员返佣状态同步触发条件
**文件**: `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`
**方法**: `processFollowOrderSettlement`

**修改前**:
```java
// 同时更新带单员的返佣状态（如果是盈利订单）
if (profit.compareTo(BigDecimal.ZERO) > 0) {
    updateLeaderRebateStatusIfNeeded(followOrder.getLeaderId(), followOrder);
}
```

**修改后**:
```java
// 同时更新带单员的返佣状态（如果是盈利或持平订单）
if (profit.compareTo(BigDecimal.ZERO) >= 0) {
    updateLeaderRebateStatusIfNeeded(followOrder.getLeaderId(), followOrder);
}
```

### 修改4：带单员返佣状态同步逻辑
**文件**: `src/main/java/com/frontapi/service/impl/SettlementServiceImpl.java`
**方法**: `updateLeaderRebateStatusIfNeeded`

**修改前**:
```java
// 检查带单员订单是否也是盈利的
if (leaderOrder.getProfit() != null && leaderOrder.getProfit().compareTo(BigDecimal.ZERO) > 0) {
    int updateResult = deliveryOrderMapper.updateRebateStatus(leaderOrder.getId(), 2);
    if (updateResult > 0) {
        log.info("跟单盈利时同步更新带单员返佣状态成功，带单员订单ID: {}, 跟单订单ID: {}", 
                leaderOrder.getId(), followOrder.getId());
    }
}
```

**修改后**:
```java
// 检查带单员订单是否也是盈利或持平的
if (leaderOrder.getProfit() != null && leaderOrder.getProfit().compareTo(BigDecimal.ZERO) >= 0) {
    int updateResult = deliveryOrderMapper.updateRebateStatus(leaderOrder.getId(), 2);
    if (updateResult > 0) {
        if (leaderOrder.getProfit().compareTo(BigDecimal.ZERO) > 0) {
            log.info("跟单盈利时同步更新带单员返佣状态成功(盈利)，带单员订单ID: {}, 跟单订单ID: {}", 
                    leaderOrder.getId(), followOrder.getId());
        } else {
            log.info("跟单盈利时同步更新带单员返佣状态成功(持平)，带单员订单ID: {}, 跟单订单ID: {}", 
                    leaderOrder.getId(), followOrder.getId());
        }
    }
}
```

## 📊 **修改逻辑说明**

### 核心变化
所有的判断条件从 `> 0` 改为 `>= 0`：
- `profit.compareTo(BigDecimal.ZERO) > 0` → `profit.compareTo(BigDecimal.ZERO) >= 0`

### 影响范围
1. **跟单订单结算**: 持平订单的返佣状态会被设为已返
2. **带单员订单结算**: 持平订单的返佣状态会被设为已返
3. **批量更新**: 当带单员订单持平时，所有相关订单都会被设为已返
4. **同步更新**: 跟单订单持平时，也会同步更新带单员的返佣状态

## 🔄 **修改后的业务流程**

### 场景1：订单盈利 (profit > 0)
```
1. 订单平仓 → profit > 0
2. 返佣状态 → rebate_status = 2 (已返) ✅
3. 日志记录 → "订单盈利，设置返佣状态为已返"
```

### 场景2：订单持平 (profit = 0)
```
1. 订单平仓 → profit = 0
2. 返佣状态 → rebate_status = 2 (已返) ✅ (新增)
3. 日志记录 → "订单持平，设置返佣状态为已返"
```

### 场景3：订单亏损 (profit < 0)
```
1. 订单平仓 → profit < 0
2. 返佣状态 → rebate_status = 1 (未返) ✅
3. 日志记录 → "订单亏损，设置返佣状态为未返"
```

## ✅ **修改效果**

### 1. 符合业务需求
- ✅ 盈利订单：返佣状态为已返
- ✅ 持平订单：返佣状态为已返 (新增)
- ✅ 亏损订单：返佣状态为未返

### 2. 逻辑一致性
- ✅ 单个订单和批量订单处理逻辑一致
- ✅ 跟单员和带单员处理逻辑一致
- ✅ 同步更新逻辑也保持一致

### 3. 日志完善
- ✅ 区分盈利、持平、亏损的日志记录
- ✅ 便于问题排查和业务监控

## 🧪 **测试验证**

### 测试场景1：持平订单
- **开仓价格**: 100.00
- **平仓价格**: 100.00
- **预期结果**: `rebate_status = 2`, 日志显示"订单持平，设置返佣状态为已返"

### 测试场景2：微小盈利订单
- **开仓价格**: 100.00
- **平仓价格**: 100.01
- **预期结果**: `rebate_status = 2`, 日志显示"订单盈利，设置返佣状态为已返"

### 测试场景3：微小亏损订单
- **开仓价格**: 100.00
- **平仓价格**: 99.99
- **预期结果**: `rebate_status = 1`, 日志显示"订单亏损，设置返佣状态为未返"

## 📝 **注意事项**

### 1. 向后兼容
- 修改只影响持平订单的处理
- 盈利和亏损订单的处理逻辑不变
- 不影响现有数据

### 2. 业务影响
- 持平订单现在会被标记为"已返"
- 这意味着持平订单不会再被重复处理
- 符合"只有亏损订单保持未返状态"的业务需求

### 3. 监控建议
- 关注持平订单的处理日志
- 监控返佣状态的分布变化
- 定期检查数据一致性

修改已完成，现在持平的订单也会被设置为已返状态！
