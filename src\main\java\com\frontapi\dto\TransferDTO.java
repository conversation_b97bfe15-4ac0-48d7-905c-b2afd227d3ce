package com.frontapi.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

import javax.validation.constraints.Positive;
import java.math.BigDecimal;

@Data
public class TransferDTO {
    private String toUid;    // 对方UID（可选）
    private String toEmail;  // 对方邮箱（可选）

    @NotNull(message = "转账金额不能为空")
    @Positive(message = "转账金额必须大于0")
    private BigDecimal amount;
    private String remark;
} 