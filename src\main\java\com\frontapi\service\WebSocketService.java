package com.frontapi.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WebSocketService {
    
    private final SimpMessagingTemplate messagingTemplate;
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);
    
    // 存储活跃的交易对订阅
    private final Map<String, Boolean> activeSubscriptions = new ConcurrentHashMap<>();
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Autowired
    public WebSocketService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
    }
    
    /**
     * 订阅交易对实时数据
     */
    public void subscribeSymbol(String symbol) {
        activeSubscriptions.put(symbol, true);
        log.info("订阅交易对: {}", symbol);

        // 启动定时推送任务
        scheduler.scheduleAtFixedRate(() -> {
            if (activeSubscriptions.get(symbol) != null) {
                pushTickerData(symbol);
                pushKlineData(symbol);
                pushDepthData(symbol);
            }
        }, 0, 5, TimeUnit.SECONDS);
    }

    /**
     * 取消订阅
     */
    public void unsubscribeSymbol(String symbol) {
        activeSubscriptions.remove(symbol);
        log.info("取消订阅交易对: {}", symbol);
    }

    /**
     * 推送ticker数据
     */
    private void pushTickerData(String symbol) {
        try {

            // 直接从Redis获取完整ticker JSON
            String tickerJson = stringRedisTemplate.opsForValue().get("binance:ticker:" + symbol);
            if (tickerJson != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Object> ticker = objectMapper.readValue(tickerJson, Map.class);
                messagingTemplate.convertAndSend("/topic/ticker/" + symbol, ticker);
            }
        } catch (Exception e) {
            log.warn("推送{} ticker数据失败: {}", symbol, e.getMessage());
        }
    }

    /**
     * 推送K线数据
     */
    private void pushKlineData(String symbol) {
        try {
            String url = String.format("https://api.binance.com/api/v3/klines?symbol=%s&interval=1m&limit=1", symbol);
            java.util.List<java.util.List<Object>> response = restTemplate.getForObject(url, java.util.List.class);

            if (response != null && !response.isEmpty()) {
                java.util.List<Object> candle = response.get(0);
                Map<String, Object> kline = new java.util.HashMap<>();
                kline.put("symbol", symbol);
                kline.put("timestamp", candle.get(0));
                kline.put("open", candle.get(1));
                kline.put("high", candle.get(2));
                kline.put("low", candle.get(3));
                kline.put("close", candle.get(4));
                kline.put("volume", candle.get(5));

                messagingTemplate.convertAndSend("/topic/kline/" + symbol, kline);
            }
        } catch (Exception e) {
            log.warn("推送{} K线数据失败: {}", symbol, e.getMessage());
        }
    }

    /**
     * 推送深度数据
     */
    private void pushDepthData(String symbol) {
        try {
            String url = String.format("https://api.binance.com/api/v3/depth?symbol=%s&limit=20", symbol);
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);

            if (response != null) {
                Map<String, Object> depth = new java.util.HashMap<>();
                depth.put("symbol", symbol);
                depth.put("bids", response.get("bids"));
                depth.put("asks", response.get("asks"));
                depth.put("timestamp", System.currentTimeMillis());

                messagingTemplate.convertAndSend("/topic/depth/" + symbol, depth);
            }
        } catch (Exception e) {
            log.warn("推送{} 深度数据失败: {}", symbol, e.getMessage());
        }
    }

    /**
     * 推送所有交易对数据
     */
    public void pushAllSymbolsData() {
        try {
            // 这里可以推送所有活跃交易对的数据
            for (String symbol : activeSubscriptions.keySet()) {
                pushTickerData(symbol);
            }
        } catch (Exception e) {
            log.warn("推送所有交易对数据失败: {}", e.getMessage());
        }
    }

    /**
     * 公共推送方法，允许外部推送任意数据到指定频道
     */
    public void sendToTopic(String topic, Object payload) {
        messagingTemplate.convertAndSend(topic, payload);
    }
} 