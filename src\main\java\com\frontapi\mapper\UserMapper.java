package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.entity.FrontUser;
import com.frontapi.vo.UserShareVO;
import com.frontapi.vo.TeamRecordVO;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserMapper extends BaseMapper<FrontUser> {
    
    @Select("SELECT COUNT(*) > 0 FROM front_user WHERE phone = #{phone}")
    boolean existsByPhone(String phone);
    
    @Select("SELECT COUNT(*) > 0 FROM front_user WHERE user_no = #{userNo}")
    boolean existsByUserNo(String userNo);
    
    @Select("SELECT COUNT(*) > 0 FROM front_user WHERE share_code = #{shareCode}")
    boolean existsByShareCode(String shareCode);
    
    @Select("SELECT * FROM front_user WHERE phone = #{phone}")
    FrontUser findByPhone(@Param("phone") String phone);
    
    @Select("SELECT COUNT(*) > 0 FROM front_user WHERE share_code = #{referrerCode}")
    boolean existsByReferrerCode(String referrerCode);
    

    @Update("UPDATE front_user SET password = #{password} WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password);
    
    FrontUser selectById(Long id);
    
    /**
     * 通过分享码查找用户
     */
    @Select("SELECT * FROM front_user WHERE share_code = #{shareCode}")
    FrontUser findByShareCode(@Param("shareCode") String shareCode);
    
    /**
     * 增加用户的团队计数
     */
    @Update("UPDATE front_user SET team_total_count = team_total_count + 1 ,team_today_count = team_today_count + 1 WHERE id = #{userId}")
    int incrementTeamCounts(@Param("userId") Long userId);
    
    Page<UserShareVO> selectShareList(Page<UserShareVO> page, @Param("shareCode") String shareCode);

    @Select("SELECT COUNT(*) > 0 FROM front_user WHERE email = #{email}")
    boolean existsByEmail(String email);

    @Select("SELECT * FROM front_user WHERE email = #{email}")
    FrontUser findByEmail(@Param("email") String email);

    @Select("SELECT * FROM front_user WHERE user_no = #{userNo}")
    FrontUser findByUserNo(@Param("userNo") String userNo);

    /**
     * 今日推荐
     */
    @Select("SELECT COUNT(*) FROM front_user WHERE referrer_code = #{shareCode} AND DATE(create_time) = CURDATE()")
    int countTodayRecommend(@Param("shareCode") String shareCode);

    /**
     * 直推总数
     */
    @Select("SELECT COUNT(*) FROM front_user WHERE referrer_code = #{shareCode}")
    int countDirectTotal(@Param("shareCode") String shareCode);

    /**
     * 有效直推
     */
    @Select("SELECT COUNT(*) FROM front_user WHERE referrer_code = #{shareCode} AND is_activated = 1")
    int countValidDirect(@Param("shareCode") String shareCode);

    /**
     * 团队记录分页
     */
    Page<TeamRecordVO> selectTeamRecords(Page<TeamRecordVO> page, @Param("shareCode") String shareCode, @Param("type") String type);

    int updateCommissionRateByUserNo(@Param("userNo") String userNo, @Param("commissionRate") BigDecimal commissionRate, @Param("oldRate") BigDecimal oldRate);

    int updateCommissionRateById(@Param("id") Long id, @Param("commissionRate") BigDecimal commissionRate);

    @Select("SELECT * FROM front_user WHERE share_code = #{shareCode}")
    FrontUser selectByShareCode(@Param("shareCode") String shareCode);

    @Select("SELECT * FROM front_user WHERE user_no = #{userNo}")
    FrontUser selectByUserNo(@Param("userNo") String userNo);

    
    @Select("SELECT * FROM front_user WHERE email = #{email} LIMIT 1")
    FrontUser selectByEmail(@Param("email") String email);

    int updateAvatar(@Param("userId") Long userId, @Param("avatar") String avatar);

}