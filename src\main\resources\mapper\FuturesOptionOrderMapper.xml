<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.FuturesOptionOrderMapper">

    <resultMap id="BaseResultMap" type="com.frontapi.entity.FuturesOptionOrder">
        <id property="id" column="id" />
        <result property="userId" column="user_id" />
        <result property="symbol" column="symbol" />
        <result property="direction" column="direction" />
        <result property="amount" column="amount" />
        <result property="period" column="period" />
        <result property="orderTime" column="order_time" />
        <result property="openPrice" column="open_price" />
        <result property="settleTime" column="settle_time" />
        <result property="closePrice" column="close_price" />
        <result property="profit" column="profit" />
        <result property="status" column="status" />
        <result property="remark" column="remark" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <!-- 持仓订单分页 -->
    <select id="selectHoldOrders" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status = 0
        ORDER BY order_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countHoldOrders" resultType="int">
        SELECT COUNT(*) FROM futures_option_order
        WHERE user_id = #{userId} AND status = 0
    </select>

    <!-- 盈利订单分页 -->
    <select id="selectProfitOrders" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status = 1 AND profit &gt; 0
        ORDER BY settle_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countProfitOrders" resultType="int">
        SELECT COUNT(*) FROM futures_option_order
        WHERE user_id = #{userId} AND status = 1 AND profit &gt; 0
    </select>

    <!-- 全平台盈利订单分页 -->
    <select id="selectAllProfitOrders" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE status = 1 AND profit &gt; 0
        ORDER BY settle_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countAllProfitOrders" resultType="int">
        SELECT COUNT(*) FROM futures_option_order
        WHERE status = 1 AND profit &gt; 0
    </select>

    <!-- 成交明细分页 -->
    <select id="selectDealOrders" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status = 1
        ORDER BY settle_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countDealOrders" resultType="int">
        SELECT COUNT(*) FROM futures_option_order
        WHERE user_id = #{userId} AND status = 1
    </select>

    <!-- MyBatis-Plus 分页：持仓 -->
    <select id="selectHoldOrdersPage" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND  status != 1
        ORDER BY order_time DESC
    </select>
    <!-- MyBatis-Plus 分页：成交 -->
    <select id="selectDealOrdersPage" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status = 1
        ORDER BY settle_time DESC
    </select>
    <!-- MyBatis-Plus 分页：全平台盈利 -->
    <select id="selectAllProfitOrdersPage" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE status = 1 AND profit &gt; 0
        ORDER BY settle_time DESC
    </select>

    <!-- 查询用户所有持仓订单（status=0），用于实时盈利推送 -->
    <select id="selectHoldOrdersByUserId" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status = 0 
        ORDER BY order_time DESC
    </select>

    <!-- 查询用户所有持仓订单（status in 0,1,2），用于实时盈利推送（含已成交/已结算但未超5分钟） -->
    <select id="selectAllHoldOrdersByUserId" resultMap="BaseResultMap">
        SELECT * FROM futures_option_order
        WHERE user_id = #{userId} AND status IN (0,1,2)
        ORDER BY order_time DESC
    </select>

    <select id="countTodayDirection" resultType="int">
      SELECT COALESCE(
        (SELECT COUNT(*) FROM delivery_order
         WHERE user_id = #{userId}
           AND direction = #{direction}
           AND create_time BETWEEN #{startTime} AND #{endTime}), 0) +
        COALESCE(
        (SELECT COUNT(*) FROM futures_option_order
         WHERE user_id = #{userId}
           AND direction = #{direction}
           AND create_time BETWEEN #{startTime} AND #{endTime}), 0)
    </select>

    <insert id="insert" parameterType="com.frontapi.entity.FuturesOptionOrder">
        INSERT INTO futures_option_order
        (user_id, symbol, direction, amount, period, order_time, open_price, settle_time, close_price, profit, status, remark, create_time, update_time)
        VALUES
        (#{userId}, #{symbol}, #{direction}, #{amount}, #{period}, #{orderTime}, #{openPrice}, #{settleTime}, #{closePrice}, #{profit}, #{status}, #{remark}, #{createTime}, #{updateTime})
    </insert>

</mapper> 