<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.DeliveryOrderMapper">
    <select id="selectTodayOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId}
          AND status = 1
        ORDER BY create_time DESC
    </select>
    <select id="selectHistoryOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId}
          AND status != 1
        ORDER BY create_time DESC
    </select>
    <select id="selectTodayOrdersPaged" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId}
          AND status = 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countTodayOrders" resultType="int">
        SELECT COUNT(*) FROM delivery_order
        WHERE user_id = #{userId}
          AND status = 1
    </select>
    <select id="selectHistoryOrdersPaged" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId}
          AND status != 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countHistoryOrders" resultType="int">
        SELECT COUNT(*) FROM delivery_order
        WHERE user_id = #{userId}
          AND status != 1
    </select>
    <select id="selectOpenOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId} AND status = 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countOpenOrders" resultType="int">
        SELECT COUNT(*) FROM delivery_order
        WHERE user_id = #{userId} AND status = 1
    </select>
    <select id="selectProfitOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId} AND status = 2 AND profit_status = 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countProfitOrders" resultType="int">
        SELECT COUNT(*) FROM delivery_order
        WHERE user_id = #{userId} AND status = 2 AND profit_status = 1
    </select>
    <select id="selectClosedOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId} AND status = 2
        ORDER BY close_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    <select id="countClosedOrders" resultType="int">
        SELECT COUNT(*) FROM delivery_order
        WHERE user_id = #{userId} AND status = 2
    </select>
    <select id="selectRecentClosedOrders" resultType="com.frontapi.vo.CopyOrderVO">
        SELECT * FROM delivery_order
        WHERE user_id = #{userId} AND status = 2
        AND close_time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY close_time DESC
        LIMIT #{limit}
    </select>
    <insert id="insertOrder" parameterType="com.frontapi.entity.DeliveryOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO delivery_order (
            user_id, leader_id, symbol, margin_amount, position_amount, lever, direction, take_profit, stop_loss, open_price, close_price, open_time, close_time, status, profit, rebate_status, profit_status, is_settlement, create_time, update_time
        ) VALUES (
            #{userId}, #{leaderId}, #{symbol}, #{marginAmount}, #{positionAmount}, #{lever}, #{direction}, #{takeProfit}, #{stopLoss}, #{openPrice}, #{closePrice}, #{openTime}, #{closeTime}, #{status}, #{profit}, #{rebateStatus}, #{profitStatus}, #{isSettlement}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="updateById" parameterType="com.frontapi.entity.DeliveryOrder">
        UPDATE delivery_order SET
            status = #{status},
            close_price = #{closePrice},
            close_time = #{closeTime},
            profit = #{profit},
            profit_status = #{profitStatus},
            is_settlement = #{isSettlement},
            rebate_status = #{rebateStatus},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="selectById" resultType="com.frontapi.entity.DeliveryOrder">
        SELECT * FROM delivery_order WHERE id = #{id}
    </select>

    <select id="selectFollowOrdersByLeaderOrderId" resultType="com.frontapi.entity.DeliveryOrder">
        SELECT d1.* FROM delivery_order d1
        INNER JOIN (
            SELECT leader_id, symbol, open_time
            FROM delivery_order
            WHERE id = #{leaderOrderId}
        ) d2 ON d1.leader_id = d2.leader_id
            AND d1.symbol = d2.symbol
            AND ABS(TIMESTAMPDIFF(SECOND, d1.open_time, d2.open_time)) &lt;= 60
        WHERE d1.id != #{leaderOrderId}
          AND d1.user_id != d1.leader_id
    </select>

    <select id="selectProfitOrdersByLeaderId" resultType="com.frontapi.entity.DeliveryOrder">
        SELECT * FROM delivery_order
        WHERE leader_id = #{leaderId}
        AND profit IS NOT NULL
        AND profit > 0
        AND rebate_status = 1
        AND profit_status != 2
        AND is_settlement = 2
    </select>

    <update id="updateSettlementStatusByLeaderOrderId">
        UPDATE delivery_order d1
        INNER JOIN (
            SELECT leader_id, symbol, open_time
            FROM delivery_order
            WHERE id = #{leaderOrderId}
        ) d2 ON d1.leader_id = d2.leader_id
            AND d1.symbol = d2.symbol
            AND d1.open_time = d2.open_time
        SET d1.is_settlement = #{settlementStatus}, d1.update_time = NOW()
    </update>
</mapper>