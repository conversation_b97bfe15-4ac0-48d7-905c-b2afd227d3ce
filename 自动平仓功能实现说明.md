# 自动平仓功能实现说明

## 功能概述

实现了基于止盈止损价格的自动平仓功能，实时监听带单人的订单，当当前交易对的价格符合止盈止损条件时，自动进行平仓。带单人平仓时，所有跟单的人也会跟着平仓，平仓后的逻辑与手动平仓完全相同。

## 核心组件

### 1. 自动平仓服务 (AutoCloseService)

#### 接口定义
```java
public interface AutoCloseService {
    void startAutoCloseMonitoring();           // 启动监控
    void stopAutoCloseMonitoring();            // 停止监控
    boolean shouldAutoClose(DeliveryOrder order, BigDecimal currentPrice);  // 判断是否需要平仓
    String getAutoCloseReason(DeliveryOrder order, BigDecimal currentPrice); // 获取平仓原因
}
```

#### 实现特点
- **定时监控**：每5秒检查一次所有持仓订单
- **异步处理**：使用线程池异步执行，不阻塞主线程
- **异常隔离**：单个订单检查失败不影响其他订单
- **详细日志**：完整的监控和平仓操作日志

### 2. 价格获取机制

#### 实现方式
```java
// 直接从Redis获取价格，与系统其他模块保持一致
private BigDecimal getCurrentPriceFromRedis(String symbol) {
    String redisKey = "binance:ticker:" + symbol;
    String tickerJson = stringRedisTemplate.opsForValue().get(redisKey);
    // 解析JSON获取lastPrice
}
```

#### 实现特点
- **统一数据源**：使用与系统其他模块相同的Redis价格数据
- **实时性**：直接获取最新的行情数据
- **简化架构**：无需额外的价格服务或API接口

## 数据库字段

### delivery_order 表相关字段
```sql
`take_profit` decimal(20,8) DEFAULT '0.00000000' COMMENT '止盈价格'
`stop_loss` decimal(20,8) DEFAULT '0.00000000' COMMENT '止损价格'
`direction` tinyint(4) DEFAULT 1 COMMENT '买涨买跌(1:买涨,2:买跌)'
`status` tinyint(4) DEFAULT 1 COMMENT '状态(1:开仓,2:平仓)'
```

### 新增查询方法
```java
// 查询所有持仓中且设置了止盈止损的订单
@Select("SELECT * FROM delivery_order WHERE status = 1 AND (take_profit > 0 OR stop_loss > 0)")
List<DeliveryOrder> selectOpenOrdersWithStopConditions();

// 查询指定带单员的所有跟单订单
@Select("SELECT * FROM delivery_order WHERE leader_id = #{leaderId} AND symbol = #{symbol} AND status = 1 AND user_id != leader_id")
List<DeliveryOrder> selectFollowerOrdersByLeader(@Param("leaderId") Long leaderId, @Param("symbol") String symbol);
```

## 止盈止损逻辑

### 买涨订单 (direction = 1)
- **止盈条件**：当前价格 >= 止盈价格
- **止损条件**：当前价格 <= 止损价格

### 买跌订单 (direction = 2)
- **止盈条件**：当前价格 <= 止盈价格
- **止损条件**：当前价格 >= 止损价格

### 判断逻辑示例
```java
// 买涨订单止盈判断
if (order.getDirection() == 1 && currentPrice.compareTo(order.getTakeProfit()) >= 0) {
    return true; // 触发止盈
}

// 买跌订单止损判断
if (order.getDirection() == 2 && currentPrice.compareTo(order.getStopLoss()) >= 0) {
    return true; // 触发止损
}
```

## 自动平仓流程

### 1. 监控流程
```
启动应用 → 自动启动监控 → 每5秒执行一次检查 → 查询持仓订单 → 获取当前价格 → 判断止盈止损条件 → 执行平仓
```

### 2. 平仓执行流程

#### A. 带单员订单平仓
```
检测到带单员订单需要平仓 → 查找所有跟单订单 → 先平仓带单员订单 → 依次平仓跟单订单 → 执行结算逻辑
```

#### B. 跟单员订单平仓
```
检测到跟单员订单需要平仓 → 直接平仓该订单 → 执行结算逻辑
```

### 3. 结算逻辑
自动平仓的结算逻辑与手动平仓完全相同：
- 返还保证金
- 扣除平仓手续费
- 分配盈利收益
- 进行佣金分配

## 配置和启动

### 1. 自动启动配置
```java
@Configuration
@EnableAsync
public class AutoCloseConfig implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {
        autoCloseService.startAutoCloseMonitoring();
    }
}
```

### 2. 配置参数
```yaml
# application.yml
auto-close:
  enabled: true              # 是否启用自动平仓功能
  startup-delay: 10          # 启动延迟时间（秒）
  check-interval: 5          # 检查间隔（秒）
  thread-pool-size: 2        # 线程池大小
```

## 使用示例

### 1. 创建带止盈止损的订单
```java
DeliveryOrder order = new DeliveryOrder();
order.setSymbol("BTC/USDT");
order.setDirection(1); // 买涨
order.setOpenPrice(new BigDecimal("43000.00"));
order.setTakeProfit(new BigDecimal("45000.00")); // 止盈价格
order.setStopLoss(new BigDecimal("41000.00"));   // 止损价格
// ... 其他字段
```

### 2. 监控日志示例
```
检查 3 个持仓订单的止盈止损条件
触发自动平仓条件，订单ID: 123, 交易对: BTC/USDT, 当前价格: 45000.00, 原因: 止盈平仓
带单员订单自动平仓，同时平仓 2 个跟单订单
跟单订单自动平仓成功，订单ID: 124
跟单订单自动平仓成功，订单ID: 125
带单员及跟单订单自动平仓完成，带单员订单ID: 123
```

## 技术特点

### 1. 高可靠性
- 异常隔离：单个订单处理失败不影响其他订单
- 事务保护：平仓操作在事务中执行
- 详细日志：便于问题排查和监控

### 2. 高性能
- 异步处理：使用线程池异步执行
- 价格缓存：减少外部API调用
- 精确查询：只查询需要监控的订单

### 3. 易扩展
- 接口设计：清晰的接口定义，易于扩展
- 配置化：监控间隔、价格源等可配置
- 模块化：各组件职责明确，易于维护

## 注意事项

### 1. 价格数据源
- 当前使用模拟价格数据
- 实际部署时需要对接真实的行情API
- 建议使用多个数据源做冗余

### 2. 系统资源
- 监控频率不宜过高，避免系统负载过大
- 价格缓存有助于减少外部API调用
- 异步处理避免阻塞主线程

### 3. 业务逻辑
- 止盈止损价格必须大于0才会被监控
- 平仓后的结算逻辑与手动平仓完全一致
- 带单员平仓会触发所有跟单订单平仓

### 4. 异常处理
- 价格获取失败时跳过该轮检查
- 平仓失败时记录错误日志但不中断监控
- 系统重启后监控会自动恢复

## 测试建议

### 1. 功能测试
- 测试不同方向订单的止盈止损判断
- 测试带单员和跟单员的平仓流程
- 测试异常情况的处理

### 2. 性能测试
- 测试大量订单的监控性能
- 测试价格缓存的有效性
- 测试系统资源占用情况

### 3. 集成测试
- 测试与现有平仓逻辑的集成
- 测试与结算系统的集成
- 测试监控系统的稳定性

这个自动平仓功能为交易系统提供了重要的风险控制机制，确保用户能够在预设的价格点自动执行平仓操作，有效控制交易风险。
