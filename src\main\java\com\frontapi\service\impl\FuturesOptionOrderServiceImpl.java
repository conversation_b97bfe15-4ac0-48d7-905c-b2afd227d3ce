package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.entity.FuturesOptionOrder;
import com.frontapi.mapper.FuturesOptionOrderMapper;
import com.frontapi.service.FuturesOptionOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.json.JSONObject;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.mapper.TradeRecordMapper;
import com.frontapi.entity.TradeRecord;
import java.time.LocalDateTime;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import com.frontapi.dto.OrderProfitDTO;
import java.util.ArrayList;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.frontapi.service.SysParamsService;
import com.frontapi.entity.SysParams;
import com.frontapi.entity.FrontUser;
import com.frontapi.entity.CommissionRecord;
import com.frontapi.mapper.CommissionRecordMapper;

@Service
public class FuturesOptionOrderServiceImpl implements FuturesOptionOrderService {
    private static final Logger logger = LoggerFactory.getLogger(FuturesOptionOrderServiceImpl.class);
    @Autowired
    private FuturesOptionOrderMapper futuresOptionOrderMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private TradeRecordMapper tradeRecordMapper;
    @Autowired
    private SysParamsService sysParamsService;
    @Autowired
    private CommissionRecordMapper commissionRecordMapper;

    @Override
    public List<FuturesOptionOrder> getHoldOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return futuresOptionOrderMapper.selectHoldOrders(userId, offset, pageSize);
    }

    @Override
    public int countHoldOrders(Long userId) {
        return futuresOptionOrderMapper.countHoldOrders(userId);
    }

    @Override
    public List<FuturesOptionOrder> getProfitOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return futuresOptionOrderMapper.selectProfitOrders(userId, offset, pageSize);
    }

    @Override
    public int countProfitOrders(Long userId) {
        return futuresOptionOrderMapper.countProfitOrders(userId);
    }

    @Override
    public List<FuturesOptionOrder> getDealOrders(Long userId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return futuresOptionOrderMapper.selectDealOrders(userId, offset, pageSize);
    }

    @Override
    public int countDealOrders(Long userId) {
        return futuresOptionOrderMapper.countDealOrders(userId);
    }

    @Override
    public List<FuturesOptionOrder> getAllProfitOrders(int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        return futuresOptionOrderMapper.selectAllProfitOrders(offset, pageSize);
    }
    @Override
    public int countAllProfitOrders() {
        return futuresOptionOrderMapper.countAllProfitOrders();
    }

    @Override
    public IPage<FuturesOptionOrder> getHoldOrdersPage(Integer page, Integer size, Long userId) {
        Page<FuturesOptionOrder> pageObj = new Page<>(page, size);
        // 这里假设你有对应的Mapper方法，或用MP的lambdaQuery
        return futuresOptionOrderMapper.selectHoldOrdersPage(pageObj, userId);
    }

    @Override
    public IPage<FuturesOptionOrder> getDealOrdersPage(Integer page, Integer size, Long userId) {
        Page<FuturesOptionOrder> pageObj = new Page<>(page, size);
        return futuresOptionOrderMapper.selectDealOrdersPage(pageObj, userId);
    }

    @Override
    public IPage<FuturesOptionOrder> getAllProfitOrdersPage(Integer page, Integer size) {
        Page<FuturesOptionOrder> pageObj = new Page<>(page, size);
        return futuresOptionOrderMapper.selectAllProfitOrdersPage(pageObj);
    }

    @Override
    @Transactional
    public boolean placeOrder(Long userId, String symbol, String direction, Double amount, Integer period) {
        java.math.BigDecimal orderAmount = amount == null ? null : new java.math.BigDecimal(amount);
        // 1. 查询余额
        java.math.BigDecimal availableBalance = frontUserMapper.selectAvailableBalance(userId);
        if (availableBalance == null || availableBalance.compareTo(orderAmount) < 0) {
            throw new RuntimeException("余额不足");
        }
        // 2. 原子扣减余额
        int updateRows = frontUserMapper.decreaseAvailableBalance(userId, orderAmount);
        if (updateRows == 0) {
            throw new RuntimeException("扣减余额失败，可能余额不足");
        }
        // 3. 插入资金明细
        TradeRecord record = new TradeRecord();
        record.setUserId(userId);
        String username = frontUserMapper.selectUsernameById(userId);
        record.setUsername(username);
        record.setTradeType("期权下单");
        record.setAmount(orderAmount.negate());
        record.setAccountType(1);
        record.setRemark("期权下单冻结资金");
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(record);
        // 4. 生成订单（原有逻辑）
        FuturesOptionOrder order = new FuturesOptionOrder();
        order.setUserId(userId);
        order.setSymbol(symbol);
        order.setDirection(direction);
        order.setAmount(orderAmount);
        order.setPeriod(period);
        order.setOrderTime(new java.util.Date());
        order.setCreateTime(new java.util.Date());
        // 从Redis获取行情JSON，解析lastPrice为开盘价，增加重试机制
        String tickerJson = null;
        int retryCount = 0;
        while (retryCount < 5) {
            tickerJson = stringRedisTemplate.opsForValue().get("binance:ticker:" + symbol);
            if (tickerJson != null) {
                break;
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            retryCount++;
        }
        if (tickerJson == null) {
            throw new RuntimeException("未获取到最新价格，无法下单");
        }
        org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
        String lastPrice = jsonObj.getString("lastPrice");
        if (lastPrice == null) {
            throw new RuntimeException("行情数据异常，无法下单");
        }
        order.setOpenPrice(new java.math.BigDecimal(lastPrice));
        order.setStatus(0); // 新订单默认持仓中
        // 其他必要字段可根据实际情况补充
        int result = futuresOptionOrderMapper.insert(order);
        return result > 0;
    }

    /**
     * 获取用户所有持仓订单的实时盈利、盈利百分比及公司备注
     * 用于SSE推送
     */
    @Override
    public List<OrderProfitDTO> getUserHoldProfit(Long userId) {
        // 查询所有持仓订单（status in 0,1,2）
        List<FuturesOptionOrder> holdOrders = futuresOptionOrderMapper.selectAllHoldOrdersByUserId(userId);
        logger.info("用户{}持仓订单数: {}", userId, holdOrders.size());
        List<OrderProfitDTO> result = new ArrayList<>();
        Date now = new Date();
        for (FuturesOptionOrder order : holdOrders) {
            int status = order.getStatus();
            // status=1 且结算时间超过5分钟不推送
            if (status == 1 && order.getSettleTime() != null) {
                if (now.getTime() - order.getSettleTime().getTime() > 5 * 60 * 1000L) {
                    continue;
                }
            }
            java.math.BigDecimal profit = null;
            java.math.BigDecimal percent = null;
            if (status == 0) {
                // 实时价格
                String tickerJson = stringRedisTemplate.opsForValue().get("binance:ticker:" + order.getSymbol().replace("/", ""));
                if (tickerJson == null) continue;
                org.json.JSONObject jsonObj = new org.json.JSONObject(tickerJson);
                String lastPrice = jsonObj.getString("lastPrice");
                if (lastPrice == null) continue;
                java.math.BigDecimal currentPrice = new java.math.BigDecimal(lastPrice);
                if ("up".equals(order.getDirection())) {
                    profit = currentPrice.subtract(order.getOpenPrice())
                        .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                        .multiply(order.getAmount());
                    percent = profit.divide(order.getAmount(), 6, java.math.RoundingMode.HALF_UP)
                        .multiply(new java.math.BigDecimal(100));
                } else {
                    profit = order.getOpenPrice().subtract(currentPrice)
                        .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                        .multiply(order.getAmount());
                    percent = profit.divide(order.getAmount(), 6, java.math.RoundingMode.HALF_UP)
                        .multiply(new java.math.BigDecimal(100));
                }
            } else {
                // status=1/2，直接用数据库结算数据
                profit = order.getProfit();
                percent = null;
                if (order.getOpenPrice() != null && order.getClosePrice() != null && order.getAmount() != null) {
                    if ("up".equals(order.getDirection())) {
                        percent = order.getClosePrice().subtract(order.getOpenPrice())
                            .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                            .multiply(new java.math.BigDecimal(100));
                    } else {
                        percent = order.getOpenPrice().subtract(order.getClosePrice())
                            .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                            .multiply(new java.math.BigDecimal(100));
                    }
                }
            }
            String companyRemark = "公司：卡塔，实时推送";
            OrderProfitDTO dto = new OrderProfitDTO(order.getId(), profit, percent, status, companyRemark, order.getSettleTime());
            logger.info("推送数据: {}", dto);
            result.add(dto);
        }
        return result;
    }

    /**
     * 结算赢单订单，处理资金返还、手续费、极差分佣、明细写入等
     * @param order 需结算的订单
     * @param closePrice 结算价
     * @param settleTime 结算时间
     */
    @Transactional
    public void settleWinOrder(FuturesOptionOrder order, java.math.BigDecimal closePrice, Date settleTime) {
        // 1. 计算收益
        java.math.BigDecimal profit;
        if ("up".equals(order.getDirection())) {
            profit = closePrice.subtract(order.getOpenPrice())
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        } else {
            profit = order.getOpenPrice().subtract(closePrice)
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        }
        // 2. 查询系统参数
        SysParams sysParams = sysParamsService.getSysParams();
        java.math.BigDecimal copyTradeFeeRate = sysParams.getCopyTradeFee().divide(new java.math.BigDecimal(100));
        java.math.BigDecimal platformFeeRate = sysParams.getPlatformFeeRate();
        // 3. 计算手续费
        java.math.BigDecimal fee = order.getAmount().multiply(copyTradeFeeRate);
        // 4. 计算极差
        java.math.BigDecimal diff = fee.multiply(new java.math.BigDecimal(100).subtract(platformFeeRate)).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
        java.math.BigDecimal platformIncome = fee.subtract(diff);
        // 5. 极差分佣递归
        java.math.BigDecimal remainCommission = diff;
        FrontUser user = frontUserMapper.selectById(order.getUserId());
        String referrer = user.getReferrerCode();
        while (referrer != null && remainCommission.compareTo(java.math.BigDecimal.ZERO) > 0) {
            FrontUser parent = frontUserMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<FrontUser>().eq("share_code", referrer));
            if (parent == null) break;
            if (parent.getCommissionRate() != null && parent.getCommissionRate().compareTo(java.math.BigDecimal.ZERO) > 0) {
                java.math.BigDecimal commission = remainCommission.multiply(parent.getCommissionRate()).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
                if (commission.compareTo(java.math.BigDecimal.ZERO) > 0) {
                    // 加到佣金账户
                    parent.setCommissionBalance(parent.getCommissionBalance().add(commission));
                    frontUserMapper.updateById(parent);
                    // 写入佣金明细
                    CommissionRecord cr = new CommissionRecord();
                    cr.setUserId(parent.getId());
                    cr.setUsername(parent.getUsername());
                    cr.setPhone(parent.getEmail()); // 存邮箱
                    cr.setCommissionAmount(commission);
                    cr.setCommissionType(1); // 佣金奖励
                    cr.setReleaseTime(LocalDateTime.now());
                    cr.setRemark("极差分佣奖励，来源订单ID:" + order.getId() + "，极差总额:" + diff + "，本级佣金比例:" + parent.getCommissionRate() + "%");
                    cr.setReleaseStatus(1);
                    cr.setCreateTime(LocalDateTime.now());
                    cr.setUpdateTime(LocalDateTime.now());
                    commissionRecordMapper.insert(cr);
                }
                remainCommission = remainCommission.subtract(commission);
            }
            referrer = parent.getReferrerCode();
        }
        // 6. 资金账户返还本金-手续费
        user.setAvailableBalance(user.getAvailableBalance().add(order.getAmount().subtract(fee)));
        frontUserMapper.updateById(user);
        // 7. 盈利部分加到profit_balance
        user.setProfitBalance(user.getProfitBalance().add(profit));
        frontUserMapper.updateById(user);
        // 8. 写入资金明细
        TradeRecord fundReturn = new TradeRecord();
        fundReturn.setUserId(order.getUserId());
        fundReturn.setUsername(user.getUsername());
        fundReturn.setTradeType("期权结算返还本金");
        fundReturn.setAmount(order.getAmount().subtract(fee));
        fundReturn.setAccountType(1);
        fundReturn.setRemark("赢单返还本金-手续费，订单ID:" + order.getId() + "，结算价:" + closePrice + "，手续费:" + fee);
        fundReturn.setCreateTime(LocalDateTime.now());
        fundReturn.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(fundReturn);
        TradeRecord profitRecord = new TradeRecord();
        profitRecord.setUserId(order.getUserId());
        profitRecord.setUsername(user.getUsername());
        profitRecord.setTradeType("期权结算盈利");
        profitRecord.setAmount(profit);
        profitRecord.setAccountType(1);
        profitRecord.setRemark("赢单盈利入账，订单ID:" + order.getId() + "，结算价:" + closePrice);
        profitRecord.setCreateTime(LocalDateTime.now());
        profitRecord.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(profitRecord);
        TradeRecord feeRecord = new TradeRecord();
        feeRecord.setUserId(order.getUserId());
        feeRecord.setUsername(user.getUsername());
        feeRecord.setTradeType("期权结算手续费");
        feeRecord.setAmount(fee.negate());
        feeRecord.setAccountType(1);
        feeRecord.setRemark("赢单扣除手续费，订单ID:" + order.getId() + "，结算价:" + closePrice);
        feeRecord.setCreateTime(LocalDateTime.now());
        feeRecord.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(feeRecord);
        // 9. 写入收益奖励明细（commission_type=3）
        if (profit.compareTo(java.math.BigDecimal.ZERO) > 0) {
            CommissionRecord profitCr = new CommissionRecord();
            profitCr.setUserId(user.getId());
            profitCr.setUsername(user.getUsername());
            profitCr.setPhone(user.getEmail()); // 存邮箱
            profitCr.setCommissionAmount(profit);
            profitCr.setCommissionType(2); // 收益奖励
            profitCr.setReleaseTime(LocalDateTime.now());
            profitCr.setRemark("赢单收益奖励，订单ID:" + order.getId() + "，结算价:" + closePrice);
            profitCr.setReleaseStatus(1);
            profitCr.setCreateTime(LocalDateTime.now());
            profitCr.setUpdateTime(LocalDateTime.now());
            commissionRecordMapper.insert(profitCr);
        }
        // 10. 更新订单表
        order.setSettleTime(settleTime);
        order.setClosePrice(closePrice);
        order.setProfit(profit);
        order.setStatus(1);
        order.setRemark("赢单结算，返还本金-手续费，盈利入账，手续费扣除，极差分佣，订单ID:" + order.getId());
        order.setUpdateTime(new Date());
        futuresOptionOrderMapper.updateById(order);
    }

    /**
     * 结算亏单订单，处理本金返还、手续费、明细写入等
     */
    @Transactional
    public void settleLoseOrder(FuturesOptionOrder order, java.math.BigDecimal closePrice, Date settleTime) {
        // 1. 计算亏损
        java.math.BigDecimal loss;
        if ("up".equals(order.getDirection())) {
            loss = order.getOpenPrice().subtract(closePrice)
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        } else {
            loss = closePrice.subtract(order.getOpenPrice())
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        }
        // 2. 查询系统参数
        SysParams sysParams = sysParamsService.getSysParams();
        java.math.BigDecimal copyTradeFeeRate = sysParams.getCopyTradeFee().divide(new java.math.BigDecimal(100));
        java.math.BigDecimal platformFeeRate = sysParams.getPlatformFeeRate();
        // 3. 计算手续费
        java.math.BigDecimal fee = order.getAmount().multiply(copyTradeFeeRate);
        // 4. 计算极差
        java.math.BigDecimal diff = fee.multiply(new java.math.BigDecimal(100).subtract(platformFeeRate)).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
        java.math.BigDecimal platformIncome = fee.subtract(diff);
        // 5. 极差分佣递归（同赢单）
        java.math.BigDecimal remainCommission = diff;
        FrontUser user = frontUserMapper.selectById(order.getUserId());
        String referrer = user.getReferrerCode();
        while (referrer != null && remainCommission.compareTo(java.math.BigDecimal.ZERO) > 0) {
            FrontUser parent = frontUserMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<FrontUser>().eq("share_code", referrer));
            if (parent == null) break;
            if (parent.getCommissionRate() != null && parent.getCommissionRate().compareTo(java.math.BigDecimal.ZERO) > 0) {
                java.math.BigDecimal commission = remainCommission.multiply(parent.getCommissionRate()).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
                if (commission.compareTo(java.math.BigDecimal.ZERO) > 0) {
                    parent.setCommissionBalance(parent.getCommissionBalance().add(commission));
                    frontUserMapper.updateById(parent);
                    CommissionRecord cr = new CommissionRecord();
                    cr.setUserId(parent.getId());
                    cr.setUsername(parent.getUsername());
                    cr.setPhone(parent.getEmail()); // 存邮箱
                    cr.setCommissionAmount(commission);
                    cr.setCommissionType(1); // 佣金奖励
                    cr.setReleaseTime(LocalDateTime.now());
                    cr.setRemark("极差分佣奖励，来源订单ID:" + order.getId() + "，极差总额:" + diff + "，本级佣金比例:" + parent.getCommissionRate() + "%");
                    cr.setReleaseStatus(1);
                    cr.setCreateTime(LocalDateTime.now());
                    cr.setUpdateTime(LocalDateTime.now());
                    commissionRecordMapper.insert(cr);
                }
                remainCommission = remainCommission.subtract(commission);
            }
            referrer = parent.getReferrerCode();
        }
        // 6. 资金账户返还剩余本金-手续费
        java.math.BigDecimal returnAmount = order.getAmount().subtract(loss).subtract(fee);
        if (returnAmount.compareTo(java.math.BigDecimal.ZERO) < 0) returnAmount = java.math.BigDecimal.ZERO;
        user.setAvailableBalance(user.getAvailableBalance().add(returnAmount));
        frontUserMapper.updateById(user);
        // 7. 写入资金明细
        TradeRecord fundReturn = new TradeRecord();
        fundReturn.setUserId(order.getUserId());
        fundReturn.setUsername(user.getUsername());
        fundReturn.setTradeType("期权结算返还本金");
        fundReturn.setAmount(returnAmount);
        fundReturn.setAccountType(1);
        fundReturn.setRemark("亏单返还本金-手续费，订单ID:" + order.getId() + "，结算价:" + closePrice + "，手续费:" + fee + "，亏损:" + loss);
        fundReturn.setCreateTime(LocalDateTime.now());
        fundReturn.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(fundReturn);
        TradeRecord feeRecord = new TradeRecord();
        feeRecord.setUserId(order.getUserId());
        feeRecord.setUsername(user.getUsername());
        feeRecord.setTradeType("期权结算手续费");
        feeRecord.setAmount(fee.negate());
        feeRecord.setAccountType(1);
        feeRecord.setRemark("亏单扣除手续费，订单ID:" + order.getId() + "，结算价:" + closePrice);
        feeRecord.setCreateTime(LocalDateTime.now());
        feeRecord.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(feeRecord);
        // 8. 更新订单表
        order.setSettleTime(settleTime);
        order.setClosePrice(closePrice);
        order.setProfit(loss.negate());
        order.setStatus(1);
        order.setRemark("亏单结算，返还本金-手续费，手续费扣除，极差分佣，订单ID:" + order.getId());
        order.setUpdateTime(new Date());
        futuresOptionOrderMapper.updateById(order);
    }

    /**
     * 结算爆仓订单，本金全部扣除，仅扣手续费，明细写入
     */
    @Transactional
    public void settleLiquidationOrder(FuturesOptionOrder order, java.math.BigDecimal closePrice, Date settleTime) {
        // 1. 查询系统参数
        SysParams sysParams = sysParamsService.getSysParams();
        java.math.BigDecimal copyTradeFeeRate = sysParams.getCopyTradeFee().divide(new java.math.BigDecimal(100));
        java.math.BigDecimal platformFeeRate = sysParams.getPlatformFeeRate();
        // 2. 计算手续费
        java.math.BigDecimal fee = order.getAmount().multiply(copyTradeFeeRate);
        // 3. 计算极差
        java.math.BigDecimal diff = fee.multiply(new java.math.BigDecimal(100).subtract(platformFeeRate)).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
        java.math.BigDecimal platformIncome = fee.subtract(diff);
        // 4. 极差分佣递归（同赢单）
        java.math.BigDecimal remainCommission = diff;
        FrontUser user = frontUserMapper.selectById(order.getUserId());
        String referrer = user.getReferrerCode();
        while (referrer != null && remainCommission.compareTo(java.math.BigDecimal.ZERO) > 0) {
            FrontUser parent = frontUserMapper.selectOne(new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<FrontUser>().eq("share_code", referrer));
            if (parent == null) break;
            if (parent.getCommissionRate() != null && parent.getCommissionRate().compareTo(java.math.BigDecimal.ZERO) > 0) {
                java.math.BigDecimal commission = remainCommission.multiply(parent.getCommissionRate()).divide(new java.math.BigDecimal(100), 6, java.math.RoundingMode.HALF_UP);
                if (commission.compareTo(java.math.BigDecimal.ZERO) > 0) {
                    parent.setCommissionBalance(parent.getCommissionBalance().add(commission));
                    frontUserMapper.updateById(parent);
                    CommissionRecord cr = new CommissionRecord();
                    cr.setUserId(parent.getId());
                    cr.setUsername(parent.getUsername());
                    cr.setPhone(parent.getEmail()); // 存邮箱
                    cr.setCommissionAmount(commission);
                    cr.setCommissionType(1); // 佣金奖励
                    cr.setReleaseTime(LocalDateTime.now());
                    cr.setRemark("极差分佣奖励，来源订单ID:" + order.getId() + "，极差总额:" + diff + "，本级佣金比例:" + parent.getCommissionRate() + "%");
                    cr.setReleaseStatus(1);
                    cr.setCreateTime(LocalDateTime.now());
                    cr.setUpdateTime(LocalDateTime.now());
                    commissionRecordMapper.insert(cr);
                }
                remainCommission = remainCommission.subtract(commission);
            }
            referrer = parent.getReferrerCode();
        }
        // 5. 仅扣手续费，不返还本金
        // 6. 写入资金明细
        TradeRecord feeRecord = new TradeRecord();
        feeRecord.setUserId(order.getUserId());
        feeRecord.setUsername(user.getUsername());
        feeRecord.setTradeType("期权结算手续费");
        feeRecord.setAmount(fee.negate());
        feeRecord.setAccountType(1);
        feeRecord.setRemark("爆仓扣除手续费，订单ID:" + order.getId() + "，结算价:" + closePrice);
        feeRecord.setCreateTime(LocalDateTime.now());
        feeRecord.setUpdateTime(LocalDateTime.now());
        tradeRecordMapper.insert(feeRecord);
        // 7. 更新订单表
        order.setSettleTime(settleTime);
        order.setClosePrice(closePrice);
        order.setProfit(order.getAmount().negate());
        order.setStatus(1);
        order.setRemark("爆仓结算，本金全部扣除，仅扣手续费，极差分佣，订单ID:" + order.getId());
        order.setUpdateTime(new Date());
        futuresOptionOrderMapper.updateById(order);
    }

    /**
     * 订单结算入口，根据盈亏自动调用赢单/亏单/爆仓结算
     */
    @Transactional
    public void settleOrderById(Long orderId, java.math.BigDecimal closePrice, Date settleTime) {
       try {
        FuturesOptionOrder order = futuresOptionOrderMapper.selectById(orderId);
        System.out.println("1结算订单1: " + order.getId() + "，状态: " + order.getStatus());
        if (order == null || order.getStatus()!=2) return;
        // 新增：未到期订单不结算
        Date now = new Date();
        Date expireTime = new Date(order.getOrderTime().getTime() + order.getPeriod() * 1000L);
        if (now.before(expireTime)) {
            System.out.println("订单未到期，不结算: " + order.getId());
            return;
        }
        System.out.println("2结算订单2: " + order.getId() + "，状态: " + order.getStatus());
        java.math.BigDecimal profit;
        if ("up".equals(order.getDirection())) {
            profit = closePrice.subtract(order.getOpenPrice())
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        } else {
            profit = order.getOpenPrice().subtract(closePrice)
                    .divide(order.getOpenPrice(), 6, java.math.RoundingMode.HALF_UP)
                    .multiply(order.getAmount());
        }
        System.out.println("3结算订单3: " + order.getId() + "，状态: " + order.getStatus());
        // 判断爆仓：亏损+手续费>=本金
        SysParams sysParams = sysParamsService.getSysParams();
        java.math.BigDecimal copyTradeFeeRate = sysParams.getCopyTradeFee().divide(new java.math.BigDecimal(100));
        java.math.BigDecimal fee = order.getAmount().multiply(copyTradeFeeRate);
        if (profit.compareTo(java.math.BigDecimal.ZERO) > 0) {
            System.out.println("结算赢单: " + order.getId());
            settleWinOrder(order, closePrice, settleTime);
        } else if (order.getAmount().add(profit).subtract(fee).compareTo(java.math.BigDecimal.ZERO) <= 0) {
            System.out.println("结算爆仓: " + order.getId());
            settleLiquidationOrder(order, closePrice, settleTime);
        } else {
            System.out.println("结算亏单: " + order.getId());
            settleLoseOrder(order, closePrice, settleTime);
        }
       } catch (Exception e) {
        System.out.println("结算订单异常: " + e.getMessage());
       }
    }

    /**
     * 只更新订单的结算价、状态、更新时间
     */
    public void updateOrderForSettle(FuturesOptionOrder order) {
        FuturesOptionOrder update = new FuturesOptionOrder();
        update.setId(order.getId());
        update.setClosePrice(order.getClosePrice());
        update.setStatus(order.getStatus());
        update.setUpdateTime(order.getUpdateTime());
        futuresOptionOrderMapper.updateById(update);
    }

    @Override
    public List<FuturesOptionOrder> getPendingSettleOrders() {
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<FuturesOptionOrder> qw = new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        qw.eq("status", 2);
        qw.orderByAsc("update_time");
        return futuresOptionOrderMapper.selectList(qw);
    }
} 