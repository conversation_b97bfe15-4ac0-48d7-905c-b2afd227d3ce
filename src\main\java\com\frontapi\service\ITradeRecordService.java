package com.frontapi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frontapi.entity.TradeRecord;

/**
 * 交易明细表 服务接口
 */
public interface ITradeRecordService extends IService<TradeRecord> {

    /**
     * 分页查询交易明细
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @param accountType 账户类型
     * @return 分页结果
     */
    IPage<TradeRecord> getTradeRecordPage(Integer page, Integer size, Long userId, Integer accountType);

    /**
     * 添加交易记录
     * @param userId 用户ID
     * @param username 用户名
     * @param tradeType 交易类型
     * @param amount 金额
     * @param accountType 账户类型
     * @param remark 备注
     */
    void addTradeRecord(Long userId, String username, String tradeType, 
                       java.math.BigDecimal amount, Integer accountType, String remark);
} 