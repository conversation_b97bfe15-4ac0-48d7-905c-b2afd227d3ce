# 级差佣金分配机制说明

## 🎯 **功能目标**

将原有的累积佣金分配模式改为级差佣金分配模式，确保佣金按照级差方式进行分配，如果没有一键跟单则跳过，使用上级的佣金比例减掉已发的佣金比例作为上级所得的佣金比例。

## 🔄 **分配模式对比**

### 原有模式（累积模式）
```
用户A（下单用户）
  ↑ 推荐关系
用户B（第1层，佣金比例20%，有一键跟单）→ 获得累积佣金：20%
  ↑ 推荐关系  
用户C（第2层，佣金比例15%，有一键跟单）→ 获得累积佣金：15%
  ↑ 推荐关系
用户D（第3层，佣金比例10%，有一键跟单）→ 获得累积佣金：10%
```

**问题**：每层都按自己的比例获得佣金，没有体现级差关系。

### 新模式（级差模式）
```
用户A（下单用户）
  ↑ 推荐关系
用户B（第1层，佣金比例20%，有一键跟单）→ 获得级差佣金：20% - 0% = 20%
  ↑ 推荐关系  
用户C（第2层，佣金比例25%，有一键跟单）→ 获得级差佣金：25% - 20% = 5%
  ↑ 推荐关系
用户D（第3层，佣金比例30%，有一键跟单）→ 获得级差佣金：30% - 25% = 5%
```

**优势**：体现真正的级差关系，上级只获得比下级多出的部分。

## 📋 **核心规则**

### 1. **级差计算公式**
```
级差佣金比例 = 当前层级佣金比例 - 已发放佣金比例
```

### 2. **分配条件**
- **必须有一键跟单**：`is_copy_trade = 1`
- **必须有佣金比例**：`commission_rate > 0`
- **级差必须为正**：`级差佣金比例 > 0`

### 3. **跳过规则**
- **无一键跟单**：跳过发放，继续向上查找
- **无佣金比例**：跳过发放，继续向上查找
- **级差为负或零**：跳过发放，继续向上查找

### 4. **已发放比例更新**
- 只有在实际发放佣金后，才更新已发放比例
- 更新为当前层级的佣金比例

## 💰 **计算示例**

### 示例1：正常级差分配

**推荐链结构**：
```
用户A（下单用户，手续费100 USDT）
  ↑ 推荐关系
用户B（第1层，佣金比例20%，有一键跟单）
  ↑ 推荐关系  
用户C（第2层，佣金比例25%，有一键跟单）
  ↑ 推荐关系
用户D（第3层，佣金比例30%，有一键跟单）
```

**分配过程**：
1. **第1层用户B**：
   - 级差比例 = 20% - 0% = 20%
   - 佣金金额 = 100 × 20% = 20 USDT ✅
   - 已发放比例更新为 20%

2. **第2层用户C**：
   - 级差比例 = 25% - 20% = 5%
   - 佣金金额 = 100 × 5% = 5 USDT ✅
   - 已发放比例更新为 25%

3. **第3层用户D**：
   - 级差比例 = 30% - 25% = 5%
   - 佣金金额 = 100 × 5% = 5 USDT ✅
   - 已发放比例更新为 30%

**总计**：20 + 5 + 5 = 30 USDT

### 示例2：包含跳过情况

**推荐链结构**：
```
用户A（下单用户，手续费100 USDT）
  ↑ 推荐关系
用户B（第1层，佣金比例20%，有一键跟单）
  ↑ 推荐关系  
用户C（第2层，佣金比例15%，无一键跟单）← 跳过
  ↑ 推荐关系
用户D（第3层，佣金比例30%，有一键跟单）
```

**分配过程**：
1. **第1层用户B**：
   - 级差比例 = 20% - 0% = 20%
   - 佣金金额 = 100 × 20% = 20 USDT ✅
   - 已发放比例更新为 20%

2. **第2层用户C**：
   - 无一键跟单，跳过发放 ⚠️
   - 已发放比例保持 20%

3. **第3层用户D**：
   - 级差比例 = 30% - 20% = 10%
   - 佣金金额 = 100 × 10% = 10 USDT ✅
   - 已发放比例更新为 30%

**总计**：20 + 0 + 10 = 30 USDT

### 示例3：级差为负的情况

**推荐链结构**：
```
用户A（下单用户，手续费100 USDT）
  ↑ 推荐关系
用户B（第1层，佣金比例30%，有一键跟单）
  ↑ 推荐关系  
用户C（第2层，佣金比例25%，有一键跟单）← 级差为负
  ↑ 推荐关系
用户D（第3层，佣金比例35%，有一键跟单）
```

**分配过程**：
1. **第1层用户B**：
   - 级差比例 = 30% - 0% = 30%
   - 佣金金额 = 100 × 30% = 30 USDT ✅
   - 已发放比例更新为 30%

2. **第2层用户C**：
   - 级差比例 = 25% - 30% = -5%
   - 级差为负，跳过发放 ⚠️
   - 已发放比例保持 30%

3. **第3层用户D**：
   - 级差比例 = 35% - 30% = 5%
   - 佣金金额 = 100 × 5% = 5 USDT ✅
   - 已发放比例更新为 35%

**总计**：30 + 0 + 5 = 35 USDT

## 🔧 **技术实现**

### 核心变量
```java
BigDecimal alreadyDistributedRate = BigDecimal.ZERO; // 已发放的佣金比例
```

### 级差计算
```java
// 计算级差佣金比例：当前层级佣金比例 - 已发放佣金比例
BigDecimal differentialRate = commissionRate.subtract(alreadyDistributedRate);
```

### 发放条件判断
```java
// 检查是否有一键跟单
if (isCopyTrade != null && isCopyTrade == 1) {
    // 检查佣金比例和级差
    if (commissionRate != null && commissionRate.compareTo(BigDecimal.ZERO) > 0) {
        if (differentialRate.compareTo(BigDecimal.ZERO) > 0) {
            // 发放佣金
            distributeCommissionToUser(referrerId, commissionAmount, orderId, level);
            // 更新已发放比例
            alreadyDistributedRate = commissionRate;
        }
    }
}
```

## 📊 **日志输出**

### 详细日志示例
```
=== 开始级差佣金分配 ===
初始已发放佣金比例: 0
第1层推荐人: ID=6, 佣金比例=20.00, 一键跟单=1, 已发放比例=0
级差计算 - 当前层级比例: 20.00, 已发放比例: 0, 级差比例: 20.00
✅ 第1层推荐人获得级差佣金: 20.00% (20.0000)
第2层推荐人: ID=5, 佣金比例=25.00, 一键跟单=1, 已发放比例=20.00
级差计算 - 当前层级比例: 25.00, 已发放比例: 20.00, 级差比例: 5.00
✅ 第2层推荐人获得级差佣金: 5.00% (5.0000)
=== 级差佣金分配完成 ===
最终已发放佣金比例: 25.00
```

## ✅ **优势总结**

1. **真正的级差关系**：体现上级比下级多获得的部分
2. **避免重复发放**：防止累积模式下的重复计算
3. **灵活跳过机制**：无一键跟单或级差为负时自动跳过
4. **清晰的日志记录**：详细记录每层的计算过程
5. **向后兼容**：不影响现有的数据结构和接口

## 🎯 **适用场景**

- **开仓佣金分配**：订单创建时基于开仓手续费
- **平仓佣金分配**：订单结算时基于平仓手续费
- **所有订单类型**：带单员订单、跟单员订单均适用
