package com.frontapi.entity;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class TransferWithdrawParamsVO {
    private BigDecimal minTransfer;
    private BigDecimal maxTransfer;
    private BigDecimal transferFee;
    private Boolean enableTransfer;

    private BigDecimal minWithdraw;
    private BigDecimal maxWithdraw;
    private BigDecimal maxAutoWithdraw;
    private BigDecimal withdrawFee;
    private Boolean enableWithdraw;
    private Boolean autoWithdraw;
    private Boolean enableInternalTransfer;
} 