package com.frontapi.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class AESUtil {
    
    private static final String ALGORITHM = "AES";
    // 使用16字节的密钥，避免加密策略限制
    private static final String AES_KEY = "FrontApiWallet2024"; // 16字符密钥
    
    /**
     * AES加密
     * @param data 待加密数据
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String data) {
        return encrypt(data, AES_KEY);
    }
    
    /**
     * AES加密
     * @param data 待加密数据
     * @param key 密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String data, String key) {
        try {
            // 确保密钥长度为16字节
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] validKey = new byte[16];
            System.arraycopy(keyBytes, 0, validKey, 0, Math.min(keyBytes.length, 16));
            
            SecretKeySpec secretKey = new SecretKeySpec(validKey, ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }
    
    /**
     * AES解密
     * @param encryptedData 加密的Base64字符串
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptedData) {
        return decrypt(encryptedData, AES_KEY);
    }
    
    /**
     * AES解密
     * @param encryptedData 加密的Base64字符串
     * @param key 密钥
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptedData, String key) {
        try {
            // 确保密钥长度为16字节
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] validKey = new byte[16];
            System.arraycopy(keyBytes, 0, validKey, 0, Math.min(keyBytes.length, 16));
            
            SecretKeySpec secretKey = new SecretKeySpec(validKey, ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }
    }
}