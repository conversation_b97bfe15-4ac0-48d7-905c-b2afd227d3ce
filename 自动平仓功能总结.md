# 自动平仓功能总结

## 功能概述

实现了完全自动化的止盈止损平仓功能，系统启动后自动开始监控，无需手动干预。

## 核心特性

### ✅ 完全自动化
- **自动启动**：应用启动时自动开启监控
- **自动停止**：应用关闭时自动停止监控
- **无需手动操作**：不需要页面调用任何接口

### ✅ 智能监控
- **实时检查**：每5秒检查一次所有持仓订单
- **精确判断**：根据买涨/买跌方向智能判断止盈止损条件
- **联动平仓**：带单员平仓时自动平仓所有跟单订单

### ✅ 高可靠性
- **异常隔离**：单个订单处理失败不影响其他订单
- **异步处理**：使用线程池异步执行，不阻塞主线程
- **详细日志**：完整的监控和平仓操作记录

## 工作流程

```
应用启动 → 延迟10秒 → 自动启动监控 → 每5秒检查一次 → 发现触发条件 → 自动执行平仓 → 继续监控
```

## 止盈止损逻辑

### 买涨订单 (direction = 1)
- **止盈**：当前价格 ≥ 止盈价格 → 自动平仓
- **止损**：当前价格 ≤ 止损价格 → 自动平仓

### 买跌订单 (direction = 2)
- **止盈**：当前价格 ≤ 止盈价格 → 自动平仓
- **止损**：当前价格 ≥ 止损价格 → 自动平仓

## 配置参数

```yaml
# src/main/resources/application.yml
auto-close:
  enabled: true              # 启用自动平仓功能
  startup-delay: 10          # 启动延迟10秒
  check-interval: 5          # 每5秒检查一次
  thread-pool-size: 2        # 使用2个线程处理
```

## 价格数据源

- **数据来源**：直接从Redis获取实时价格
- **Redis键格式**：`binance:ticker:{symbol}`
- **价格字段**：JSON中的`lastPrice`字段
- **统一数据源**：与系统其他模块使用相同的价格数据

## 数据库要求

订单必须设置止盈或止损价格才会被监控：
```sql
-- 只监控设置了止盈或止损的持仓订单
SELECT * FROM delivery_order 
WHERE status = 1 AND (take_profit > 0 OR stop_loss > 0)
```

## 使用方式

### 1. 创建订单时设置止盈止损
```java
DeliveryOrder order = new DeliveryOrder();
order.setTakeProfit(new BigDecimal("45000.00")); // 止盈价格
order.setStopLoss(new BigDecimal("41000.00"));   // 止损价格
// 保存订单后，系统自动开始监控
```

### 2. 系统自动处理
- 无需任何手动操作
- 系统自动监控价格变化
- 触发条件时自动执行平仓
- 平仓后执行与手动平仓相同的结算逻辑

## 监控日志示例

### 正常监控
```
2024-07-25 10:00:00 INFO - ✓ 自动平仓监控已成功启动
2024-07-25 10:00:05 DEBUG - 检查 5 个持仓订单的止盈止损条件
```

### 触发平仓
```
2024-07-25 10:05:15 INFO - 触发自动平仓条件，订单ID: 100, 交易对: BTC/USDT, 当前价格: 45000.00, 原因: 止盈平仓
2024-07-25 10:05:15 INFO - 带单员订单自动平仓，同时平仓 3 个跟单订单
2024-07-25 10:05:16 INFO - ✓ 带单员及跟单订单自动平仓完成，带单员订单ID: 100
```

## 技术实现

### 核心组件
- `AutoCloseService` - 自动平仓服务接口
- `AutoCloseServiceImpl` - 核心监控和平仓逻辑
- `AutoCloseConfig` - 自动启动配置

### 关键方法
- `startAutoCloseMonitoring()` - 启动监控
- `shouldAutoClose()` - 判断是否需要平仓
- `executeAutoClose()` - 执行自动平仓
- `getCurrentPriceFromRedis()` - 从Redis获取价格

## 安全机制

### 1. 启动安全
- 延迟启动确保系统完全初始化
- 启动失败不影响应用正常运行

### 2. 运行安全
- 异常隔离，单个订单失败不影响其他订单
- 价格获取失败时跳过该轮检查
- 详细的错误日志记录

### 3. 停止安全
- 应用关闭时自动停止监控
- 优雅关闭，等待正在处理的任务完成

## 性能优化

### 1. 精确查询
- 只查询设置了止盈止损的持仓订单
- 避免无效的价格检查

### 2. 异步处理
- 使用线程池异步执行平仓操作
- 不阻塞监控主线程

### 3. 价格缓存
- 缓存价格数据，减少重复获取
- 支持配置缓存时间

## 注意事项

### 1. 价格数据源
- 直接使用Redis中的实时价格数据
- 与系统其他模块使用相同的数据源
- 确保Redis中有对应交易对的价格数据

### 2. 系统资源
- 监控间隔建议不要设置太小
- 根据订单量调整线程池大小

### 3. 业务逻辑
- 止盈止损价格必须大于0才会被监控
- 平仓后的结算逻辑与手动平仓完全一致

## 总结

这个自动平仓功能实现了：

✅ **完全自动化** - 无需手动干预，系统自动运行
✅ **智能监控** - 实时检查止盈止损条件
✅ **联动平仓** - 带单员平仓时跟单员自动跟随
✅ **高可靠性** - 异常隔离，详细日志
✅ **易配置** - 支持多种配置参数
✅ **高性能** - 异步处理，精确查询

现在用户只需要在创建订单时设置好止盈止损价格，系统就会自动监控并在合适的时机执行平仓，为用户提供了重要的风险控制工具。
