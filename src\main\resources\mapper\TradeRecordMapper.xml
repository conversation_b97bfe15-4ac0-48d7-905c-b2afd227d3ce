<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.TradeRecordMapper">
    <insert id="insert" parameterType="com.frontapi.entity.TradeRecord">
        INSERT INTO trade_record
        (user_id, username, trade_type, amount, account_type, remark, create_time, update_time)
        VALUES
        (#{userId}, #{username}, #{tradeType}, #{amount}, #{accountType}, #{remark}, #{createTime}, #{updateTime})
    </insert>
</mapper> 