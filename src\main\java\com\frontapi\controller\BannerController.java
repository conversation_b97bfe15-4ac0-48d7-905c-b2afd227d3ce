package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.SysBanner;
import com.frontapi.mapper.BannerMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/banner")
@RequiredArgsConstructor
public class BannerController {
    
    private final BannerMapper bannerMapper;
    
    @GetMapping("/list")
    public ApiResponse<List<SysBanner>> getBannerList() {
        List<SysBanner> bannerList = bannerMapper.selectBannerList();
        return ApiResponse.success(bannerList);
    }
} 