package com.frontapi.controller;

import com.frontapi.dto.RechargeDTO;
import com.frontapi.service.RechargeService;
import com.frontapi.common.Result;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/api/recharge")
@RequiredArgsConstructor
public class RechargeController {

    private final RechargeService rechargeService;

    @PostMapping("/create")
    public Result<?> createRecharge(@RequestBody RechargeDTO dto) {
        // 验证金额
        if (dto.getAmount().compareTo(new BigDecimal("100")) < 0) {
            return Result.fail("充值金额不能小于100元");
        }
        
        // 验证凭证
        if (StringUtils.isEmpty(dto.getProofImage())) {
            return Result.fail("请上传转账凭证");
        }
        
        rechargeService.createRecharge(dto);
        return Result.ok();
    }

    @GetMapping("/check-pending")
    public Result<Boolean> checkPendingRecharge() {
        return Result.ok(rechargeService.hasPendingRecharge());
    }
} 