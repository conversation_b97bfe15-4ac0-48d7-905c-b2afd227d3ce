package com.frontapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.TradeRecord;
import com.frontapi.service.ITradeRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 交易明细表 前端控制器
 */
@RestController
@RequestMapping("/api/trade-record")
@RequiredArgsConstructor
public class TradeRecordController {

    private final ITradeRecordService tradeRecordService;
    private final UserService userService;

    /**
     * 分页查询交易明细
     */
    @GetMapping("/list")
    public ApiResponse<IPage<TradeRecord>> getTradeRecordPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer accountType) {
        
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        
        IPage<TradeRecord> result = tradeRecordService.getTradeRecordPage(page, size, currentUser.getId(), accountType);
        return ApiResponse.success(result);
    }

    /**
     * 根据ID查询交易明细
     */
    @GetMapping("/{id}")
    public ApiResponse<TradeRecord> getTradeRecordById(@PathVariable Long id) {
        TradeRecord tradeRecord = tradeRecordService.getById(id);
        if (tradeRecord == null) {
            return ApiResponse.error("交易记录不存在");
        }
        return ApiResponse.success(tradeRecord);
    }

    /**
     * 添加交易记录
     */
    @PostMapping("/add")
    public ApiResponse<Void> addTradeRecord(@RequestBody TradeRecord tradeRecord) {
        try {
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                return ApiResponse.error("用户未登录");
            }
            
            tradeRecordService.addTradeRecord(
                currentUser.getId(),
                currentUser.getUsername(),
                tradeRecord.getTradeType(),
                tradeRecord.getAmount(),
                tradeRecord.getAccountType(),
                tradeRecord.getRemark()
            );
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 删除交易记录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteTradeRecord(@PathVariable Long id) {
        boolean result = tradeRecordService.removeById(id);
        if (result) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除失败");
        }
    }
} 