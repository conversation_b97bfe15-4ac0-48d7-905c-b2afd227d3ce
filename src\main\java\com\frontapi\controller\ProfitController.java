package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.service.DeliveryOrderService;
import com.frontapi.vo.UserVO;
import com.frontapi.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/copy/profit")
public class ProfitController {
    @Autowired
    private DeliveryOrderService deliveryOrderService;
    @Autowired
    private UserService userService;

    @GetMapping("/summary")
    public ApiResponse<Map<String, Object>> getProfitSummary() {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        Long userId = currentUser.getId();
        BigDecimal todayProfit = deliveryOrderService.getTodayProfit(userId);
        BigDecimal totalProfit = deliveryOrderService.getTotalProfit(userId);
        Map<String, Object> data = new HashMap<>();
        data.put("today_profit", todayProfit);
        data.put("total_profit", totalProfit);
        return ApiResponse.success(data);
    }
} 