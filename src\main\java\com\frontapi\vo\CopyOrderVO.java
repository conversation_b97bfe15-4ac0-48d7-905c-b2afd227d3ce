package com.frontapi.vo;

import java.math.BigDecimal;
import java.util.Date;

public class CopyOrderVO {
    private Long id;
    private Long userId;
    private Long leaderId;
    private String symbol;
    private BigDecimal marginAmount;
    private BigDecimal positionAmount;
    private Integer lever;
    private Integer direction;
    private BigDecimal takeProfit;
    private BigDecimal stopLoss;
    private BigDecimal openPrice;
    private BigDecimal closePrice;
    private Date openTime;
    private Date closeTime;
    private Integer status;
    private BigDecimal profit;
    private Integer rebateStatus;
    private Integer profitStatus;
    private Date createTime;
    private Date updateTime;
    // getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    public Long getLeaderId() { return leaderId; }
    public void setLeaderId(Long leaderId) { this.leaderId = leaderId; }
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    public BigDecimal getMarginAmount() { return marginAmount; }
    public void setMarginAmount(BigDecimal marginAmount) { this.marginAmount = marginAmount; }
    public BigDecimal getPositionAmount() { return positionAmount; }
    public void setPositionAmount(BigDecimal positionAmount) { this.positionAmount = positionAmount; }
    public Integer getLever() { return lever; }
    public void setLever(Integer lever) { this.lever = lever; }
    public Integer getDirection() { return direction; }
    public void setDirection(Integer direction) { this.direction = direction; }
    public BigDecimal getTakeProfit() { return takeProfit; }
    public void setTakeProfit(BigDecimal takeProfit) { this.takeProfit = takeProfit; }
    public BigDecimal getStopLoss() { return stopLoss; }
    public void setStopLoss(BigDecimal stopLoss) { this.stopLoss = stopLoss; }
    public BigDecimal getOpenPrice() { return openPrice; }
    public void setOpenPrice(BigDecimal openPrice) { this.openPrice = openPrice; }
    public BigDecimal getClosePrice() { return closePrice; }
    public void setClosePrice(BigDecimal closePrice) { this.closePrice = closePrice; }
    public Date getOpenTime() { return openTime; }
    public void setOpenTime(Date openTime) { this.openTime = openTime; }
    public Date getCloseTime() { return closeTime; }
    public void setCloseTime(Date closeTime) { this.closeTime = closeTime; }
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    public BigDecimal getProfit() { return profit; }
    public void setProfit(BigDecimal profit) { this.profit = profit; }
    public Integer getRebateStatus() { return rebateStatus; }
    public void setRebateStatus(Integer rebateStatus) { this.rebateStatus = rebateStatus; }
    public Integer getProfitStatus() { return profitStatus; }
    public void setProfitStatus(Integer profitStatus) { this.profitStatus = profitStatus; }
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
} 