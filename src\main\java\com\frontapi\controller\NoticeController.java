package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.Notice;
import com.frontapi.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/notice")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    @GetMapping("/list")
    public ApiResponse<Page<Notice>> list(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
            
        Page<Notice> pageInfo = new Page<>(page, size);
        
        LambdaQueryWrapper<Notice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Notice::getStatus, 1)  // 只查询启用的公告
               .orderByDesc(Notice::getIsTop)  // 置顶的排在前面
               .orderByAsc(Notice::getSort)    // 按排序号升序
               .orderByDesc(Notice::getPublishTime); // 最后按发布时间倒序
        
        Page<Notice> result = noticeService.page(pageInfo, wrapper);
        return ApiResponse.success(result);
    }

    @GetMapping("/detail/{id}")
    public ApiResponse<Notice> detail(@PathVariable Long id) {
        // 1. 获取公告详情
        Notice notice = noticeService.getById(id);
        if (notice == null || notice.getStatus() != 1) {
            return ApiResponse.error("公告不存在");
        }
        
        // 2. 处理公告内容的换行
        if (notice.getContent() != null) {
            // 统一换行符
            notice.setContent(notice.getContent().replaceAll("\\r\\n", "\n")
                                               .replaceAll("\\r", "\n"));
        }
        
        return ApiResponse.success(notice);
    }
} 