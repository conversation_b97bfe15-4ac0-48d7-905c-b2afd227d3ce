package com.frontapi.service.impl;

import com.frontapi.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import javax.xml.bind.DatatypeConverter;
import com.frontapi.config.SmsProperties;
import com.frontapi.mapper.UserMapper;
import com.frontapi.exception.SmsException;

@Service
@Slf4j
public class SmsServiceImpl implements SmsService {
    
    private static final long SEND_INTERVAL = 5 * 60; // 5分钟间隔，单位：秒
    
    private final Map<String, String> codeCacheMap = new ConcurrentHashMap<>();
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private SmsProperties smsProperties;
    
    @Autowired
    private RestTemplate restTemplate;
    
    private final UserMapper userMapper;
    
    @Autowired
    public SmsServiceImpl(UserMapper userMapper) {
        this.userMapper = userMapper;
    }
    
    @Override
    public void sendCode(String phone) {
        // 检查发送频率限制
        String rateLimitKey = "sms:ratelimit:" + phone;
        if (redisTemplate != null) {
            // 检查是否存在发送记录
            Boolean hasKey = redisTemplate.hasKey(rateLimitKey);
            if (Boolean.TRUE.equals(hasKey)) {
                throw new RuntimeException("请等待5分钟后再次获取验证码");
            }
        }
        
        String code = RandomStringUtils.randomNumeric(6);
        codeCacheMap.put(phone, code);
        
        // 构建短信内容，添加签名
        String content = String.format("%s你本次注册的验证码是%s", smsProperties.getSign(), code);
//        log.info(content);
        // 调用发送短信接口
        boolean sent =sendSmsMessage(phone, content);
        if (sent) {
            log.info("验证码短信发送成功 - 手机号: {}, 验证码: {}", phone, code);
            // 发送成功后，记录发送时间
            if (redisTemplate != null) {
                redisTemplate.opsForValue().set(rateLimitKey, "", SEND_INTERVAL, TimeUnit.SECONDS);
            }
        } else {
            log.error("验证码短信发送失败 - 手机号: {}", phone);
            // 发送失败时抛出异常
            throw new RuntimeException("验证码发送失败，请稍后重试");
        }
    }

    @Override
    public boolean verifyCode(String phone, String code) {
        String cachedCode = codeCacheMap.get(phone);
        if (cachedCode != null && cachedCode.equals(code)) {
            codeCacheMap.remove(phone);
            return true;
        }
        return false;
    }

    @Override
    public boolean sendVerifyCode(String phone, String code) {
        try {
            if (redisTemplate == null) {
                log.warn("Redis template is not available, falling back to local cache");
                codeCacheMap.put(phone, code);
                
                // 构建短信内容并发送，添加签名
                String content = String.format("%s你本次注册的验证码是：%s", smsProperties.getSign(), code);
                boolean sent = sendSmsMessage(phone, content);
                if (!sent) {
                    log.error("验证码短信发送失败 - 手机号: {}", phone);
//                    throw new RuntimeException("验证码发送失败，请稍后重试");
                }
                
                return true;
            }
            
            String key = "sms:verify:" + phone;
            redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
            
            // 构建短信内容并发送，添加签名
            String content = String.format("%s你本次注册的验证码是%s", smsProperties.getSign(), code);
            boolean sent = sendSmsMessage(phone, content);
            if (!sent) {
                log.error("验证码短信发送失败 - 手机号: {}", phone);
                throw new RuntimeException("验证码发送失败，请稍后重试");
            }
            
            return true;
        } catch (Exception e) {
            log.error("发送验证码失败", e);
            throw new RuntimeException("验证码发送失败，请稍后重试");
        }
    }

    @Override
    public boolean sendSmsMessage(String mobile, String content) {
        try {
            // 生成时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            
            // 生成签名 (USER_ID + PASSWORD + timestamp)
            String signStr = smsProperties.getUserId() + smsProperties.getPassword() + timestamp;
            String sign = generateMD5(signStr);
            
            // 构建请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("action", "send");
            params.add("rt", "json");
            params.add("userid", smsProperties.getEnterpriseId());
            params.add("timestamp", timestamp);
            params.add("sign", sign);
            params.add("mobile", mobile);
            params.add("content", content);
            params.add("sendTime", "");
            params.add("extno", "");
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            // 发送请求
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(
                smsProperties.getUrl() + "?action=send",
                request,
                String.class
            );
            
            // 解析响应
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> result = mapper.readValue(response.getBody(), Map.class);
            
            boolean success = "Success".equals(result.get("ReturnStatus"));
            if (success) {
                log.info("短信发送成功 - 手机号: {}, 任务ID: {}", mobile, result.get("TaskID"));
                return true;
            } else {
                String errorMsg = (String) result.get("Message");
                log.error("短信发送失败 - 手机号: {}, 错误信息: {}", mobile, errorMsg);
                throw new SmsException("发送失败");
            }
        }   catch (Exception e) {
            log.error("发送短信时发生错误: {}", e.getMessage());
            throw new SmsException("发送失败");
        }
    }
    
    private String generateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return DatatypeConverter.printHexBinary(messageDigest).toLowerCase();
        } catch (Exception e) {
            log.error("生成MD5失败", e);
            throw new RuntimeException("生成MD5失败", e);
        }
    }

    @Override
    public void sendLoginCode(String phone) {
        // 验证手机号是否已注册
        if (!userMapper.existsByPhone(phone)) {
            throw new RuntimeException("该手机号尚未注册，请先注册");
        }
        
        // 生成并发送验证码
        String code = RandomStringUtils.randomNumeric(6);
        codeCacheMap.put(phone, code);
        
        // 构建短信内容，添加签名
        String content = String.format("%s你本次注册的验证码是%s", smsProperties.getSign(), code);
        // 调用发送短信接口
        boolean sent = sendSmsMessage(phone, content);
        if (sent) {
            log.info("登录验证码短信发送成功 - 手机号: {}, 验证码: {}", phone, code);
        } else {
            log.error("登录验证码短信发送失败 - 手机号: {}", phone);
            throw new SmsException("发送失败");
        }
    }
} 