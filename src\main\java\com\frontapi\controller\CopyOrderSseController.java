package com.frontapi.controller;

import com.frontapi.service.DeliveryOrderService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import com.frontapi.dto.CopyOrderProfitDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 带单订单实时盈利推送控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/copy/order")
public class CopyOrderSseController {

    @Autowired
    private DeliveryOrderService deliveryOrderService;
    
    @Autowired
    private UserService userService;

    // 存储用户的SSE连接
    private static final ConcurrentHashMap<Long, SseEmitter> userConnections = new ConcurrentHashMap<>();

    // 定时任务执行器
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    // 静态实例，用于在静态方法中调用服务
    private static CopyOrderSseController instance;

    // 构造函数中设置静态实例
    public CopyOrderSseController() {
        instance = this;
        // 启动定时任务
        if (scheduler != null) {
            scheduler.scheduleAtFixedRate(() -> {
                if (instance != null) {
                    instance.pushProfitToAllUsers();
                }
            }, 5, 5, TimeUnit.SECONDS);
        }
    }

    /**
     * 建立SSE连接，推送持仓订单实时盈利
     */
    @GetMapping("/hold/profit/stream")
    public SseEmitter streamHoldProfit(@RequestParam String token) {
        try {
            // 验证用户身份
            UserVO currentUser = userService.getCurrentUserInfo();
            if (currentUser == null) {
                // log.warn("用户未登录，拒绝SSE连接");
                return null;
            }

            Long userId = currentUser.getId();
            // log.info("用户{}建立带单盈利SSE连接", userId);

            // 创建SSE连接，超时时间30分钟
            SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);
            
            // 存储连接
            userConnections.put(userId, emitter);

            // 连接完成或超时时清理
            emitter.onCompletion(() -> {
                userConnections.remove(userId);
                // log.info("用户{}的带单盈利SSE连接已完成", userId);
            });

            emitter.onTimeout(() -> {
                userConnections.remove(userId);
                // log.info("用户{}的带单盈利SSE连接超时", userId);
            });

            emitter.onError((ex) -> {
                userConnections.remove(userId);
                // log.error("用户{}的带单盈利SSE连接出错", userId, ex);
            });

            // 立即推送一次数据
            this.pushProfitToUser(userId, emitter);

            return emitter;

        } catch (Exception e) {
            log.error("建立带单盈利SSE连接失败", e);
            return null;
        }
    }

    /**
     * 向所有连接的用户推送实时盈利数据
     */
    private void pushProfitToAllUsers() {
        userConnections.forEach((userId, emitter) -> {
            try {
                this.pushProfitToUser(userId, emitter);
            } catch (Exception e) {
                log.error("推送用户{}的带单盈利数据失败", userId, e);
                userConnections.remove(userId);
            }
        });
    }

    /**
     * 向指定用户推送实时盈利数据
     */
    private void pushProfitToUser(Long userId, SseEmitter emitter) {
        try {
            // 获取用户持仓订单的实时盈利数据
            List<CopyOrderProfitDTO> profitData = deliveryOrderService.getUserHoldOrdersProfit(userId);

            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(profitData);

            emitter.send(SseEmitter.event()
                    .name("profit-update")
                    .data(jsonData));

        } catch (IOException e) {
            log.error("推送数据到用户{}失败", userId, e);
            userConnections.remove(userId);
        } catch (Exception e) {
            log.error("获取用户{}的带单盈利数据失败", userId, e);
        }
    }
}
