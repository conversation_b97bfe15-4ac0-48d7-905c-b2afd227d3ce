# 自动平仓功能使用示例

## 1. 创建带止盈止损的订单

### 示例1：买涨订单
```java
// 创建买涨订单，设置止盈止损
DeliveryOrder buyOrder = new DeliveryOrder();
buyOrder.setUserId(1L);
buyOrder.setLeaderId(1L);
buyOrder.setSymbol("BTC/USDT");
buyOrder.setDirection(1); // 买涨
buyOrder.setOpenPrice(new BigDecimal("43000.00"));
buyOrder.setTakeProfit(new BigDecimal("45000.00")); // 止盈：价格上涨到45000时平仓
buyOrder.setStopLoss(new BigDecimal("41000.00"));   // 止损：价格下跌到41000时平仓
buyOrder.setPositionAmount(new BigDecimal("1.0"));
buyOrder.setMarginAmount(new BigDecimal("4300.00"));
buyOrder.setStatus(1); // 持仓中

// 保存订单后，自动平仓系统会开始监控
```

### 示例2：买跌订单
```java
// 创建买跌订单，设置止盈止损
DeliveryOrder sellOrder = new DeliveryOrder();
sellOrder.setUserId(2L);
sellOrder.setLeaderId(2L);
sellOrder.setSymbol("ETH/USDT");
sellOrder.setDirection(2); // 买跌
sellOrder.setOpenPrice(new BigDecimal("2500.00"));
sellOrder.setTakeProfit(new BigDecimal("2300.00")); // 止盈：价格下跌到2300时平仓
sellOrder.setStopLoss(new BigDecimal("2700.00"));   // 止损：价格上涨到2700时平仓
sellOrder.setPositionAmount(new BigDecimal("2.0"));
sellOrder.setMarginAmount(new BigDecimal("5000.00"));
sellOrder.setStatus(1); // 持仓中
```

## 2. 自动平仓触发场景

### 场景1：买涨订单止盈
```
订单信息：
- 交易对：BTC/USDT
- 方向：买涨 (direction=1)
- 开仓价格：43000.00
- 止盈价格：45000.00
- 止损价格：41000.00

价格变化：
43000 → 43500 → 44000 → 44500 → 45000 ✓ 触发止盈

系统行为：
1. 监控系统检测到 BTC/USDT 价格达到 45000
2. 判断：当前价格(45000) >= 止盈价格(45000) ✓
3. 执行自动平仓，原因：止盈平仓
4. 如果是带单员订单，同时平仓所有跟单订单
```

### 场景2：买跌订单止损
```
订单信息：
- 交易对：ETH/USDT
- 方向：买跌 (direction=2)
- 开仓价格：2500.00
- 止盈价格：2300.00
- 止损价格：2700.00

价格变化：
2500 → 2550 → 2600 → 2650 → 2700 ✓ 触发止损

系统行为：
1. 监控系统检测到 ETH/USDT 价格达到 2700
2. 判断：当前价格(2700) >= 止损价格(2700) ✓
3. 执行自动平仓，原因：止损平仓
```

## 3. 带单员和跟单员联动平仓

### 示例场景
```
带单员订单：
- 订单ID：100
- 用户ID：2 (带单员)
- 领导ID：2 (自己)
- 交易对：BTC/USDT
- 止盈价格：45000

跟单员订单：
- 订单ID：101, 用户ID：3, 领导ID：2
- 订单ID：102, 用户ID：4, 领导ID：2
- 订单ID：103, 用户ID：5, 领导ID：2

自动平仓流程：
1. BTC/USDT 价格达到 45000，触发带单员订单止盈
2. 系统检测到这是带单员订单 (user_id = leader_id)
3. 查找所有跟单该带单员的订单 (leader_id=2, symbol=BTC/USDT, status=1)
4. 执行平仓顺序：
   - 先平仓带单员订单 (ID: 100)
   - 依次平仓跟单订单 (ID: 101, 102, 103)
5. 所有订单都执行相同的结算逻辑
```

## 4. 配置参数

### 自动平仓配置
```yaml
# application.yml
auto-close:
  enabled: true              # 启用自动平仓功能
  startup-delay: 10          # 启动延迟10秒
  check-interval: 5          # 每5秒检查一次
  thread-pool-size: 2        # 使用2个线程处理
```

### 价格数据源
- **数据来源**：直接从Redis获取实时价格
- **Redis键格式**：`binance:ticker:{symbol}`
- **价格字段**：JSON中的`lastPrice`字段
- **无需额外配置**：使用系统现有的价格数据

## 5. 监控日志示例

### 正常监控日志
```
2024-07-25 10:00:00 INFO  - 自动平仓监控已启动，检查间隔: 5秒
2024-07-25 10:00:05 DEBUG - 检查 5 个持仓订单的止盈止损条件
2024-07-25 10:00:05 DEBUG - 生成模拟价格，交易对: BTC/USDT, 基础价格: 43000.00, 当前价格: 43150.25
2024-07-25 10:00:10 DEBUG - 检查 5 个持仓订单的止盈止损条件
```

### 触发平仓日志
```
2024-07-25 10:05:15 INFO  - 触发自动平仓条件，订单ID: 100, 交易对: BTC/USDT, 当前价格: 45000.00, 原因: 止盈平仓
2024-07-25 10:05:15 INFO  - 开始执行自动平仓，订单ID: 100, 当前价格: 45000.00, 原因: 止盈平仓
2024-07-25 10:05:15 INFO  - 带单员订单自动平仓，同时平仓 3 个跟单订单
2024-07-25 10:05:16 INFO  - 跟单订单自动平仓成功，订单ID: 101
2024-07-25 10:05:16 INFO  - 跟单订单自动平仓成功，订单ID: 102
2024-07-25 10:05:16 INFO  - 跟单订单自动平仓成功，订单ID: 103
2024-07-25 10:05:16 INFO  - 带单员及跟单订单自动平仓完成，带单员订单ID: 100
```

## 6. 测试用例

### 测试止盈止损判断
```java
@Test
public void testAutoCloseConditions() {
    AutoCloseService autoCloseService = new AutoCloseServiceImpl();
    
    // 买涨订单
    DeliveryOrder buyOrder = createBuyOrder();
    
    // 测试各种价格情况
    assertFalse(autoCloseService.shouldAutoClose(buyOrder, new BigDecimal("42000"))); // 正常范围
    assertTrue(autoCloseService.shouldAutoClose(buyOrder, new BigDecimal("45000")));  // 触发止盈
    assertTrue(autoCloseService.shouldAutoClose(buyOrder, new BigDecimal("41000")));  // 触发止损
    
    assertEquals("止盈平仓", autoCloseService.getAutoCloseReason(buyOrder, new BigDecimal("45000")));
    assertEquals("止损平仓", autoCloseService.getAutoCloseReason(buyOrder, new BigDecimal("41000")));
}
```

## 7. 配置说明

### 监控配置
```java
// 监控间隔：5秒（可根据需要调整）
scheduler.scheduleWithFixedDelay(this::monitorOrders, 0, 5, TimeUnit.SECONDS);

// 线程池配置：2个线程
ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
```

### 价格配置
```java
// 支持的交易对及基础价格
basePrices.put("BTC/USDT", new BigDecimal("43000.00"));
basePrices.put("ETH/USDT", new BigDecimal("2500.00"));
basePrices.put("BNB/USDT", new BigDecimal("320.00"));
// 实际部署时需要对接真实行情数据源
```

## 8. 注意事项

### 订单要求
- 订单状态必须为持仓中 (status = 1)
- 必须设置止盈价格 (take_profit > 0) 或止损价格 (stop_loss > 0)
- 价格设置要合理，避免立即触发

### 系统要求
- 确保应用启动时自动平仓监控已启动
- 监控系统异常时不会影响正常交易
- 价格数据源稳定可靠

### 业务逻辑
- 自动平仓的结算逻辑与手动平仓完全一致
- 带单员平仓会触发所有跟单订单平仓
- 平仓原因会记录在系统日志中

这个自动平仓功能为用户提供了重要的风险控制工具，能够在预设条件下自动执行平仓操作，有效保护用户资金安全。
