package com.frontapi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.frontapi.common.Result;
import com.frontapi.dto.WithdrawDTO;
import com.frontapi.entity.SysParams;
import com.frontapi.mapper.SysParamsMapper;
import com.frontapi.service.WithdrawService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/withdraw")
@RequiredArgsConstructor
public class WithdrawController {

    private final WithdrawService withdrawService;
    private final SysParamsMapper sysParamsMapper;
    private final UserService userService;

    @PostMapping("/create")
    public Result<?> createWithdraw(@RequestBody WithdrawDTO withdrawDTO) {
        // 获取当前登录用户信息
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        try {
            withdrawService.createWithdraw(currentUser.getId(), withdrawDTO);
            return Result.ok();
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

        
    @GetMapping("/params")
    public Result<?> getWithdrawParams() {
        SysParams params = sysParamsMapper.selectOne(new QueryWrapper<>());
        return Result.ok(params);
    }
} 