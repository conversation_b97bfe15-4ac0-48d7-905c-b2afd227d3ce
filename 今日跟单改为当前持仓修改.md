# 今日跟单改为当前持仓修改

## 🎯 **修改目标**

将"今日跟单"功能改为"当前持仓"，不再按时间筛选，而是按订单状态筛选，显示所有持仓中的订单。

## 🔍 **修改前后对比**

### 修改前的逻辑
```
今日跟单：DATE(create_time) = CURDATE() AND status = 1  ← 只显示今天创建且持仓的订单
历史跟单：DATE(create_time) < CURDATE()                ← 只显示昨天及之前的所有订单
```

**问题**：
- 昨天创建但今天仍在持仓的订单不会显示在"今日跟单"中
- 用户无法看到完整的持仓情况

### 修改后的逻辑
```
当前持仓：status = 1                                   ← 显示所有持仓中的订单
历史跟单：status != 1                                  ← 显示所有已完成的订单
```

**优势**：
- 显示所有持仓订单，无论创建时间
- 用户可以看到完整的持仓情况
- 逻辑更加直观和合理

## 🔧 **具体修改内容**

### 1. 后端SQL查询修改

**文件**: `src/main/resources/mapper/DeliveryOrderMapper.xml`

#### 1.1 今日订单查询（无分页）
**修改前**:
```xml
<select id="selectTodayOrders" resultType="com.frontapi.vo.CopyOrderVO">
    SELECT * FROM delivery_order
    WHERE user_id = #{userId}
      AND DATE(create_time) = CURDATE()
    ORDER BY create_time DESC
</select>
```

**修改后**:
```xml
<select id="selectTodayOrders" resultType="com.frontapi.vo.CopyOrderVO">
    SELECT * FROM delivery_order
    WHERE user_id = #{userId}
      AND status = 1
    ORDER BY create_time DESC
</select>
```

#### 1.2 今日订单分页查询
**修改前**:
```xml
<select id="selectTodayOrdersPaged" resultType="com.frontapi.vo.CopyOrderVO">
    SELECT * FROM delivery_order
    WHERE user_id = #{userId}
      AND DATE(create_time) = CURDATE()
    ORDER BY create_time DESC
    LIMIT #{offset}, #{limit}
</select>
```

**修改后**:
```xml
<select id="selectTodayOrdersPaged" resultType="com.frontapi.vo.CopyOrderVO">
    SELECT * FROM delivery_order
    WHERE user_id = #{userId}
      AND status = 1
    ORDER BY create_time DESC
    LIMIT #{offset}, #{limit}
</select>
```

#### 1.3 今日订单计数查询
**修改前**:
```xml
<select id="countTodayOrders" resultType="int">
    SELECT COUNT(*) FROM delivery_order
    WHERE user_id = #{userId}
      AND DATE(create_time) = CURDATE()
</select>
```

**修改后**:
```xml
<select id="countTodayOrders" resultType="int">
    SELECT COUNT(*) FROM delivery_order
    WHERE user_id = #{userId}
      AND status = 1
</select>
```

### 2. 前端页面修改

**文件**: `pages/copy/mycopy.vue`

#### 2.1 页面标签文本
**修改前**:
```html
>今日跟单</view>
```

**修改后**:
```html
>当前持仓</view>
```

#### 2.2 数据处理逻辑
**修改前**:
```javascript
if (type === 'today') {
  // 今日跟单只显示持仓中的订单
  const holdingList = list.filter(item => item.status === 1);
  console.log(`今日跟单原始数据${list.length}条，过滤后持仓数据${holdingList.length}条`);
  
  this.todayList = holdingList;
}
```

**修改后**:
```javascript
if (type === 'today') {
  // 当前持仓订单（后端已按status=1筛选，无需前端过滤）
  console.log(`当前持仓数据${list.length}条`);
  
  this.todayList = list;
}
```

#### 2.3 注释和日志文本更新
- `计算今日跟单的未实现盈亏` → `计算当前持仓的未实现盈亏`
- `更新今日跟单列表` → `更新当前持仓列表`
- `在今日列表中查找订单` → `在持仓列表中查找订单`
- `更新今日订单的实时盈利` → `更新持仓订单的实时盈利`
- `从今日列表中移除` → `从持仓列表中移除`

## 📊 **业务逻辑说明**

### 页面功能重新定义
1. **当前持仓**：显示所有状态为1（持仓中）的订单，无论创建时间
2. **历史跟单**：显示所有状态不为1（已完成）的订单，无论创建时间

### 订单状态说明
- **status = 0**: 开仓处理中
- **status = 1**: 持仓中 ← 显示在"当前持仓"
- **status = 2**: 已平仓 ← 显示在"历史跟单"
- **status = 3**: 平仓处理中 ← 显示在"历史跟单"

## 🎯 **用户体验改进**

### 修改前的问题
- 昨天创建但今天仍持仓的订单不显示在"今日跟单"中
- 用户需要在历史记录中查找持仓订单
- 逻辑不够直观

### 修改后的优势
- ✅ 所有持仓订单都显示在"当前持仓"中
- ✅ 用户可以快速查看所有持仓情况
- ✅ 逻辑更加直观和合理
- ✅ 符合用户的使用习惯

## 🧪 **测试场景**

### 测试场景1：跨日持仓订单
- **操作**：用户昨天创建订单，今天仍在持仓
- **修改前**：订单不显示在"今日跟单"中
- **修改后**：订单显示在"当前持仓"中 ✅

### 测试场景2：今天创建的持仓订单
- **操作**：用户今天创建订单，仍在持仓
- **修改前**：订单显示在"今日跟单"中
- **修改后**：订单显示在"当前持仓"中 ✅

### 测试场景3：今天平仓的订单
- **操作**：用户今天平仓了订单
- **修改前**：订单可能不显示在任何地方
- **修改后**：订单显示在"历史跟单"中 ✅

### 测试场景4：实时盈利更新
- **操作**：持仓订单的实时盈利变化
- **修改前**：只更新今天创建的订单
- **修改后**：更新所有持仓订单 ✅

## 📱 **前端界面变化**

### 标签页标题
```
修改前：[今日跟单] [历史跟单]
修改后：[当前持仓] [历史跟单]
```

### 数据显示逻辑
```
修改前：
- 今日跟单：今天创建且持仓的订单
- 历史跟单：昨天及之前的所有订单

修改后：
- 当前持仓：所有持仓中的订单
- 历史跟单：所有已完成的订单
```

## 🔄 **数据流程**

### 当前持仓数据流程
```
用户点击"当前持仓"
    ↓
前端调用 /api/copy/order/list?type=today
    ↓
后端执行 WHERE user_id = ? AND status = 1
    ↓
返回所有持仓中的订单
    ↓
前端显示持仓列表（无需过滤）
```

### 历史跟单数据流程
```
用户点击"历史跟单"
    ↓
前端调用 /api/copy/order/list?type=history
    ↓
后端执行 WHERE user_id = ? AND status != 1
    ↓
返回所有已完成的订单
    ↓
前端显示历史列表
```

## 📋 **相关文件清单**

### 修改的文件
1. **DeliveryOrderMapper.xml** - 修改SQL查询条件
2. **mycopy.vue** - 修改前端显示逻辑和文本

### 不变的文件
1. **CopyOrderController.java** - 接口逻辑保持不变
2. **DeliveryOrderServiceImpl.java** - 服务逻辑保持不变
3. **数据库表结构** - 无需修改

## 📈 **预期效果**

### 功能完整性
- ✅ 用户可以看到所有持仓订单
- ✅ 历史记录包含所有已完成订单
- ✅ 实时盈利更新覆盖所有持仓订单

### 用户体验
- ✅ 逻辑更加直观
- ✅ 符合用户预期
- ✅ 操作更加便捷

### 系统稳定性
- ✅ 减少前端过滤逻辑
- ✅ 后端查询更加精确
- ✅ 性能更加优化

现在"当前持仓"会显示所有持仓中的订单，无论创建时间，用户体验更加合理！
