package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frontapi.entity.SysBanner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface BannerMapper extends BaseMapper<SysBanner> {
    
    @Select("SELECT * FROM sys_banner WHERE status = 1 ORDER BY sort ASC, id DESC")
    List<SysBanner> selectBannerList();
} 