package com.frontapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frontapi.entity.RechargeRecord;
import com.frontapi.mapper.RechargeRecordMapper;
import com.frontapi.service.RechargeService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import com.frontapi.dto.RechargeDTO;
import com.frontapi.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class RechargeServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord> implements RechargeService {

    private final RechargeRecordMapper rechargeRecordMapper;
    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRecharge(RechargeDTO dto) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }

        // 检查是否有待审核记录
        if (hasPendingRecharge()) {
            throw new BusinessException("您有待审核的充值记录，请等待审核完成");
        }

        RechargeRecord record = new RechargeRecord();
        record.setUserId(currentUser.getId());
        record.setUsername(currentUser.getUsername() != null ? currentUser.getUsername() : "注册用户");
        record.setPhone(currentUser.getPhone());
        record.setAmount(dto.getAmount());
        record.setProofImage(dto.getProofImage());
        record.setRechargeType(1);  // 设置充值类型默认值为1
        record.setAuditStatus(0);   // 0-待审核
        record.setRemark("用户充值");
        LocalDateTime now = LocalDateTime.now();
        record.setUpdateTime(now);
        record.setCreateTime(now);
        rechargeRecordMapper.insert(record);
    }

    @Override
    public boolean hasPendingRecharge() {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return false;
        }

        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RechargeRecord::getUserId, currentUser.getId())
               .eq(RechargeRecord::getAuditStatus, 0);  // 0-待审核
        
        return rechargeRecordMapper.selectCount(wrapper) > 0;
    }
} 