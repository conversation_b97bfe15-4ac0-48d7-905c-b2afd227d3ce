package com.frontapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.frontapi.entity.CommissionRecord;
import com.frontapi.vo.CommissionStatsVO;
import com.frontapi.vo.ProfitSummaryVO;

public interface CommissionRecordService extends IService<CommissionRecord> {
    CommissionStatsVO getCommissionStats(Long userId);

    /**
     * 获取佣金和收益汇总数据
     * @param userId 用户ID
     * @return 佣金和收益汇总
     */
    ProfitSummaryVO getProfitSummary(Long userId);
}