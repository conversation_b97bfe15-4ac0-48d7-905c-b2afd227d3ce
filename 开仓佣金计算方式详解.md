# 开仓佣金计算方式详解

## 概述

开仓佣金计算是在用户下单成功后立即进行的，基于开仓手续费作为佣金基数，按推荐链向上分配给有一键跟单的推荐人。

## 计算流程

### 1. 开仓手续费计算

**公式**：
```
开仓手续费 = 成交数量 × 开仓价格 × 手续费率 ÷ 100
```

**参数说明**：
- `成交数量`：订单的持仓量 (positionAmount)
- `开仓价格`：订单的开仓价格 (openPrice)  
- `手续费率`：系统配置的手续费率，默认0.04%

**示例**：
```
成交数量：0.1 BTC
开仓价格：118,118 USDT
手续费率：0.04%
开仓手续费 = 0.1 × 118,118 × 0.04% = 4.7247 USDT
```

### 2. 佣金分配逻辑

#### 2.1 推荐链查找
从下单用户开始，向上查找推荐链，最多查找10层：

```
用户A (下单用户)
  ↑ 推荐关系
用户B (第1层推荐人)
  ↑ 推荐关系  
用户C (第2层推荐人)
  ↑ 推荐关系
用户D (第3层推荐人)
...
```

#### 2.2 佣金比例累积规则

**核心规则**：只有开启了一键跟单的推荐人才能获得佣金，佣金按累积比例计算。

**累积逻辑**：
1. 从第1层开始向上查找
2. 每层推荐人的佣金比例都会累积
3. 遇到有一键跟单的推荐人时，发放累积的佣金，然后累积归零
4. 继续向上查找，重新开始累积

#### 2.3 佣金计算公式

```
佣金金额 = 开仓手续费 × 累积佣金比例 ÷ 100
```

## 实际案例分析

### 案例1：简单推荐链

**用户关系**：
- 用户7 (下单用户) ← 用户6 (20%佣金，有一键跟单) ← 用户5 (15%佣金，有一键跟单) ← 用户1 (5%佣金，无一键跟单)

**计算过程**：
```
用户7下单：成交数量 0.01269917，开仓价格 118,118，手续费率 0.04%
开仓手续费 = 0.01269917 × 118,118 × 0.04% = 0.6000 USDT

佣金分配：
第1层 用户6：佣金比例20%，有一键跟单
  累积比例 = 20%
  佣金金额 = 0.6000 × 20% = 0.1200 USDT ✅ 发放给用户6
  累积归零

第2层 用户5：佣金比例15%，有一键跟单  
  累积比例 = 15%
  佣金金额 = 0.6000 × 15% = 0.0900 USDT ✅ 发放给用户5
  累积归零

第3层 用户1：佣金比例5%，无一键跟单
  累积比例 = 5%
  无一键跟单，不发放佣金，继续累积
```

### 案例2：累积佣金

**用户关系**：
- 用户A (下单) ← 用户B (10%，无跟单) ← 用户C (15%，无跟单) ← 用户D (20%，有跟单)

**计算过程**：
```
开仓手续费 = 100 USDT

第1层 用户B：10%，无一键跟单 → 累积比例 = 10%
第2层 用户C：15%，无一键跟单 → 累积比例 = 10% + 15% = 25%  
第3层 用户D：20%，有一键跟单 → 累积比例 = 25% + 20% = 45%
  佣金金额 = 100 × 45% = 45 USDT ✅ 发放给用户D
  累积归零
```

## 关键特性

### 1. 累积分配机制
- **不是每层都分配**：只有有一键跟单的推荐人才能获得佣金
- **累积计算**：会累积所有上级的佣金比例，一次性发放给有跟单的推荐人
- **归零重置**：发放后累积比例归零，重新开始累积

### 2. 一键跟单要求
- **必要条件**：推荐人必须开启一键跟单 (is_copy_trade = 1)
- **业务逻辑**：只有参与跟单的推荐人才能获得佣金奖励

### 3. 佣金比例设置
- **个人设置**：每个用户都有自己的佣金比例 (commission_rate)
- **动态调整**：管理员可以调整用户的佣金比例
- **累积上限**：理论上没有上限，取决于推荐链的深度和比例设置

## 数据库记录

### 1. 佣金记录表 (commission_record)
```sql
INSERT INTO commission_record (
    user_id,           -- 获得佣金的用户ID
    username,          -- 用户名
    phone,             -- 邮箱地址
    commission_amount, -- 佣金金额
    commission_type,   -- 佣金类型：1=推荐佣金
    remark,           -- 备注：订单123第1层推荐佣金
    release_status,   -- 发放状态：1=已发放
    release_time,     -- 发放时间
    create_time,      -- 创建时间
    update_time       -- 更新时间
)
```

### 2. 交易记录表 (trade_record)
```sql
INSERT INTO trade_record (
    user_id,      -- 用户ID
    username,     -- 用户名
    trade_type,   -- 交易类型：手续费扣除
    amount,       -- 金额（负数表示扣除）
    account_type, -- 账户类型：2=跟单账户
    remark,       -- 备注：订单123手续费
    create_time,  -- 创建时间
    update_time   -- 更新时间
)
```

## 配置参数

### 1. 系统参数
- **手续费率** (copy_trade_fee)：默认0.04%，可在系统参数表中配置
- **最大查找层级**：硬编码为10层，防止无限循环

### 2. 用户参数
- **佣金比例** (commission_rate)：用户表中的字段，单位为%
- **一键跟单** (is_copy_trade)：0=未开启，1=已开启

## 异常处理

### 1. 计算异常
- 手续费计算失败：使用默认费率10%
- 佣金分配失败：记录错误日志，不影响订单创建

### 2. 数据异常
- 推荐人不存在：跳过该层，继续向上查找
- 佣金比例为空：视为0%，不参与累积
- 推荐链循环：最大查找10层后自动停止

## 日志记录

系统会详细记录佣金计算过程：
```
开始处理开仓手续费扣除和佣金分配，订单ID: 123, 用户ID: 7
开仓手续费计算 - 订单ID: 123, 成交数量: 0.01269917, 开仓价格: 118118.00000000, 手续费率: 0.04%, 开仓手续费: 0.6000
第1层推荐人: ID=6, 佣金比例=20.00, 一键跟单=1
成功发放佣金给用户6, 金额: 0.1200, 层级: 1, 订单ID: 123
第1层推荐人有一键跟单，发放佣金后累积归零
第2层推荐人: ID=5, 佣金比例=15.00, 一键跟单=1
成功发放佣金给用户5, 金额: 0.0900, 层级: 2, 订单ID: 123
第2层推荐人有一键跟单，发放佣金后累积归零
佣金分配完成，最终累积佣金比例: 5.00
```
