package com.frontapi.controller;

import com.frontapi.common.Result;
import com.frontapi.exception.BusinessException;
import com.frontapi.service.UserService;
import com.frontapi.service.UserWalletAddressService;

import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
@RestController
@RequestMapping("/api/wallet")
@RequiredArgsConstructor
public class WalletController {

    @Autowired
    private UserWalletAddressService userWalletAddressService;
    private final UserService userService;
    @GetMapping("/address")
    public Result<Map<String, String>> getWalletAddress() {

        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            throw new BusinessException("用户未登录");
        }
        String address = userWalletAddressService.getOrCreateBscAddress(currentUser.getId());
        
        Map<String, String> result = new HashMap<>();
        result.put("address", address);
        
        return Result.ok(result);
    }
} 