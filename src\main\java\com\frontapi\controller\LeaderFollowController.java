package com.frontapi.controller;

import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.FrontUser;
import com.frontapi.entity.CopyConfig;
import com.frontapi.mapper.FrontUserMapper;
import com.frontapi.mapper.CopyConfigMapper;
import com.frontapi.mapper.DeliveryOrderMapper;

import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
import java.util.Date;

import java.util.Map;

@RestController
@RequestMapping("/api/leader")
public class LeaderFollowController {

    @Autowired
    private FrontUserMapper frontUserMapper;
    @Autowired
    private CopyConfigMapper copyConfigMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private DeliveryOrderMapper deliveryOrderMapper;


    @PostMapping("/follow")
    public ApiResponse<?> followLeader(@RequestBody Map<String, Object> req) {
        UserVO userVO = userService.getCurrentUserInfo();
        if (userVO == null) {
            return ApiResponse.error("用户未登录");
        }
        FrontUser user = frontUserMapper.selectById(userVO.getId());
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }
        Long leaderId = Long.valueOf(req.get("leaderId").toString());
        if (user.getId().equals(leaderId)) {
            return ApiResponse.error("不能跟单自己");
        }
        FrontUser leader = frontUserMapper.selectById(leaderId);
        if (leader == null || leader.getIsLeader() == null || leader.getIsLeader() != 1) {
            return ApiResponse.error("目标用户不是带单员");
        }
        if (user.getIsFollowing() != null && user.getIsFollowing() == 1) {
            return ApiResponse.error("你已跟单其他带单员");
        }
        if (leader.getCopyConfigId() == null) {
            return ApiResponse.error("带单员未配置跟单参数");
        }
        CopyConfig config = copyConfigMapper.selectById(leader.getCopyConfigId());
        if (config == null) {
            return ApiResponse.error("带单员配置不存在");
        }
        BigDecimal balance = user.getCopyTradeBalance();
        if (balance == null) balance = BigDecimal.ZERO;
        if (balance.compareTo(config.getMinFollowAmount()) < 0) {
            return ApiResponse.error("跟单账户余额低于最低跟单金额");
        }
        if (config.getMaxFollowAmount().compareTo(BigDecimal.ZERO) > 0 &&
            balance.compareTo(config.getMaxFollowAmount()) > 0) {
            return ApiResponse.error("跟单账户余额高于最高跟单金额");
        }
        // 更新用户表
        user.setIsFollowing(1);
        user.setFollowStartTime(new Date()); // 记录一键跟单开始时间
        user.setLeaderId(leaderId);
        user.setCopyTradeFrozenStatus(1);
        frontUserMapper.updateById(user);
        return ApiResponse.success();
    }

    @PostMapping("/unfollow")
    public ApiResponse<?> unfollowLeader() {
        UserVO userVO = userService.getCurrentUserInfo();
        if (userVO == null) {
            return ApiResponse.error("用户未登录");
        }
        FrontUser user = frontUserMapper.selectById(userVO.getId());
        if (user == null) {
            return ApiResponse.error("用户不存在");
        }

        // 检查是否有未平仓订单
        int count = deliveryOrderMapper.countOpenOrderByUserId(user.getId());
        if (count > 0) {
            return ApiResponse.error("有未平仓订单，不能取消跟单");
        }

        // 验证锁仓时间
        if (user.getIsFollowing() == 1 && user.getLeaderId() != null && user.getLeaderId() > 0) {
            // 获取带单员信息
            FrontUser leader = frontUserMapper.selectById(user.getLeaderId());
            if (leader != null && leader.getCopyConfigId() != null) {
                CopyConfig config = copyConfigMapper.selectById(leader.getCopyConfigId());
                if (config != null && config.getLockTime() > 0) {
                    // 检查锁仓时间
                    if (user.getFollowStartTime() != null) {
                        long followDays = (System.currentTimeMillis() - user.getFollowStartTime().getTime()) / (1000 * 60 * 60 * 24);
                        if (followDays < config.getLockTime()) {
                            return ApiResponse.error("锁仓期未满，还需等待 " + (config.getLockTime() - followDays) + " 天才能取消跟单");
                        }
                    }
                }
            }
        }

        // 更新用户表
        user.setIsFollowing(0);
        user.setFollowStartTime(null); // 清空跟单开始时间
        user.setLeaderId(0L); // 取消跟单时清空leader_id
        user.setCopyTradeFrozenStatus(0);
        frontUserMapper.updateById(user);
        return ApiResponse.success();
    }

}