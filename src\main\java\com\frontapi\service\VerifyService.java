package com.frontapi.service;

import com.frontapi.dto.VerifyImageResponse;
import com.frontapi.dto.VerifyCheckResponse;

public interface VerifyService {
    /**
     * 生成验证图片
     */
    VerifyImageResponse generateVerifyImage();

    /**
     * 检查滑动验证
     */
    VerifyCheckResponse checkVerify(Integer moveX, String key);

    /**
     * 检查验证token是否有效
     */
    boolean checkToken(String token);
} 