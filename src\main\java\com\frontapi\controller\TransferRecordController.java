package com.frontapi.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frontapi.dto.ApiResponse;
import com.frontapi.entity.TransferRecord;
import com.frontapi.service.TransferRecordService;
import com.frontapi.service.UserService;
import com.frontapi.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/transfer")
@RequiredArgsConstructor
public class TransferRecordController {

    private static final Logger log = LoggerFactory.getLogger(TransferRecordController.class);

    private final TransferRecordService transferRecordService;
    private final UserService userService;

    @GetMapping("/list")
    public ApiResponse<Page<TransferRecord>> getTransferList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(defaultValue = "1") Integer type // 1=转入，2=转出
    ) {
        UserVO currentUser = userService.getCurrentUserInfo();
        if (currentUser == null) {
            return ApiResponse.error("用户未登录");
        }
        log.info("查询用户转账记录: userId={}, page={}, pageSize={}, type={}", currentUser.getId(), page, pageSize, type);
        Page<TransferRecord> records;
        if (type == 1) {
            // 查询转入明细
            records = transferRecordService.getTransferInList(currentUser.getId(), page, pageSize);
        } else {
            // 查询转出明细
            records = transferRecordService.getTransferOutList(currentUser.getId(), page, pageSize);
        }
        log.info("查询结果: total={}, records={}", records.getTotal(), records.getRecords());
        return ApiResponse.success(records);
    }
} 