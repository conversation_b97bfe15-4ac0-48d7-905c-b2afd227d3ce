package com.frontapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frontapi.entity.CommissionRecord;
import com.frontapi.vo.CommissionStatsVO;
import com.frontapi.vo.ProfitSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDateTime;
import java.math.BigDecimal;

@Mapper
public interface CommissionRecordMapper extends BaseMapper<CommissionRecord> {
    CommissionStatsVO getCommissionStats(Long userId);

    /**
     * 获取佣金和收益汇总数据
     * @param userId 用户ID
     * @return 佣金和收益汇总
     */
    ProfitSummaryVO getProfitSummary(Long userId);

    /**
     * 统计指定时间范围内的佣金总额
     * @param userId 用户ID
     * @param type 佣金类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 佣金总额
     */
    BigDecimal sumCommissionAmount(@Param("userId") Long userId,
                             @Param("type") int type,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime);

    /**
     * 统计今日总收益
     */
    BigDecimal sumTodayCommission(@Param("userId") Long userId,
                                  @Param("startTime") java.time.LocalDateTime startTime,
                                  @Param("endTime") java.time.LocalDateTime endTime);
}