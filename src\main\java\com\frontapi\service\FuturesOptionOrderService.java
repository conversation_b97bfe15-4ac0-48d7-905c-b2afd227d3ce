package com.frontapi.service;

import com.frontapi.entity.FuturesOptionOrder;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.frontapi.dto.OrderProfitDTO;

public interface FuturesOptionOrderService {
    List<FuturesOptionOrder> getHoldOrders(Long userId, int page, int pageSize);
    int countHoldOrders(Long userId);

    List<FuturesOptionOrder> getProfitOrders(Long userId, int page, int pageSize);
    int countProfitOrders(Long userId);

    List<FuturesOptionOrder> getDealOrders(Long userId, int page, int pageSize);
    int countDealOrders(Long userId);

    List<FuturesOptionOrder> getAllProfitOrders(int page, int pageSize);
    int countAllProfitOrders();

    IPage<FuturesOptionOrder> getHoldOrdersPage(Integer page, Integer size, Long userId);
    IPage<FuturesOptionOrder> getDealOrdersPage(Integer page, Integer size, Long userId);
    IPage<FuturesOptionOrder> getAllProfitOrdersPage(Integer page, Integer size);
    boolean placeOrder(Long userId, String symbol, String direction, Double amount, Integer period);

    /**
     * 获取用户所有持仓订单的实时盈利、盈利百分比及公司备注
     * 用于SSE推送
     */
    List<OrderProfitDTO> getUserHoldProfit(Long userId);
    void updateOrderForSettle(com.frontapi.entity.FuturesOptionOrder order);
     List<FuturesOptionOrder> getPendingSettleOrders();
    void settleOrderById(Long orderId, java.math.BigDecimal closePrice, java.util.Date settleTime);
} 