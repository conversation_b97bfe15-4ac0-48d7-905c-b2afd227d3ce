package com.frontapi.config;

import com.frontapi.service.AutoCloseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PreDestroy;

/**
 * 自动平仓配置类
 * 应用启动时自动开启自动平仓监控，应用关闭时自动停止监控
 */
@Slf4j
@Configuration
@EnableAsync
public class AutoCloseConfig implements CommandLineRunner {

    @Autowired
    private AutoCloseService autoCloseService;

    @Value("${auto-close.enabled:true}")
    private boolean autoCloseEnabled;

    @Value("${auto-close.startup-delay:10}")
    private int startupDelay;

    @Override
    public void run(String... args) throws Exception {
        if (!autoCloseEnabled) {
            log.info("自动平仓功能已禁用，跳过启动");
            return;
        }

        try {
            // 延迟启动，确保系统完全初始化
            if (startupDelay > 0) {
                log.info("自动平仓监控将在 {} 秒后启动", startupDelay);
                Thread.sleep(startupDelay * 1000L);
            }

            // 应用启动时自动开启自动平仓监控
            autoCloseService.startAutoCloseMonitoring();
            log.info("✓ 自动平仓监控已成功启动");

        } catch (Exception e) {
            log.error("✗ 启动自动平仓监控失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 应用关闭时自动停止监控
     */
    @PreDestroy
    public void shutdown() {
        try {
            if (autoCloseEnabled) {
                autoCloseService.stopAutoCloseMonitoring();
                log.info("✓ 自动平仓监控已停止");
            }
        } catch (Exception e) {
            log.error("✗ 停止自动平仓监控失败", e);
        }
    }
}
