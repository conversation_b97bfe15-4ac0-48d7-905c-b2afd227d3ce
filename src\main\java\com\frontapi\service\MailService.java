package com.frontapi.service;

import com.frontapi.util.EmailUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MailService {

    @Autowired
    private EmailUtils mailUtil;

    public void sendVerifyCode(String to,String subject, String code) {
        String content = "您的验证码是：" + code + "，请在5分钟内完成验证。";
        try {
            mailUtil.sendResetPasswordEmail(to, subject, content);
        } catch (Exception e) {
            // 记录日志
            System.err.println("发送邮件失败，收件人：" + to + "，原因：" + e.getMessage());
            if (e.getMessage() != null && e.getMessage().contains("550")) {
                throw new RuntimeException("收件人邮箱不存在或不可用，请检查邮箱地址");
            }
            throw new RuntimeException("邮件发送失败：" + e.getMessage());
        }
    }
} 