package com.frontapi.service;

/**
 * 跟单关系清除服务接口
 * 用于在一键平仓和自动平仓时清除不满足条件的跟单关系
 */
public interface FollowRelationCleanupService {
    
    /**
     * 清除指定带单员的不满足条件的跟单关系
     * @param leaderId 带单员ID
     * @return 清除的跟单关系数量
     */
    int cleanupInvalidFollowRelations(Long leaderId);
    
    /**
     * 检查并清除单个跟单用户的关系
     * @param followerId 跟单用户ID
     * @param leaderId 带单员ID
     * @return true-清除了关系，false-关系仍然有效
     */
    boolean checkAndCleanupSingleFollowRelation(Long followerId, Long leaderId);

    /**
     * 清除指定带单员的不满足条件的跟单关系（结算后版本）
     * 只修改跟单状态，不影响账户余额，确保佣金分配完成后再清除关系
     * @param leaderId 带单员ID
     * @return 清除的跟单关系数量
     */
    int cleanupInvalidFollowRelationsAfterSettlement(Long leaderId);
}
